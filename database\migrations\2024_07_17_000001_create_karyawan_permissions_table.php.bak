<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('karyawan_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('karyawan_id')->constrained('karyawan')->onDelete('cascade');
            $table->string('permission_type'); // 'approve_cuti', 'view_absensi', 'manage_jadwal', etc.
            $table->string('scope_type'); // 'all', 'entitas', 'departemen', 'divisi', 'custom'
            $table->json('scope_values')->nullable(); // Array of IDs for the scope
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes for performance
            $table->index(['karyawan_id', 'permission_type', 'is_active']);
            $table->index(['scope_type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('karyawan_permissions');
    }
};
