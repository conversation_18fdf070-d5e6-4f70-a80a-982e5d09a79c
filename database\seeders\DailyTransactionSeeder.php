<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use App\Models\Category;
use Carbon\Carbon;

class DailyTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $outlets = Outlet::all();
        $categories = Category::all();

        if ($outlets->isEmpty() || $categories->isEmpty()) {
            $this->command->warn('Please run OutletSeeder and CategorySeeder first!');
            return;
        }

        $fnbCategory = $categories->where('name', 'FnB')->first();
        $vooCategory = $categories->where('name', 'VOO')->first();

        // Generate data untuk 3 bulan terakhir
        $startDate = Carbon::now()->subMonths(3)->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        $transactions = [];

        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            foreach ($outlets as $outlet) {
                // Skip weekend untuk beberapa transaksi
                if ($date->isWeekend() && rand(1, 3) == 1) {
                    continue;
                }

                // FnB Transactions
                if ($fnbCategory) {
                    // Revenue FnB (penjualan makanan/minuman)
                    $transactions[] = [
                        'outlet_id' => $outlet->id,
                        'category_id' => $fnbCategory->id,
                        'transaction_date' => $date->format('Y-m-d'),
                        'description' => $this->getFnBRevenueDescription(),
                        'type' => 'revenue',
                        'amount' => rand(500000, 2500000), // 500k - 2.5jt
                        'notes' => 'Penjualan harian FnB',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    // Expense FnB (bahan baku, operasional)
                    if (rand(1, 3) == 1) { // Tidak setiap hari ada expense
                        $transactions[] = [
                            'outlet_id' => $outlet->id,
                            'category_id' => $fnbCategory->id,
                            'transaction_date' => $date->format('Y-m-d'),
                            'description' => $this->getFnBExpenseDescription(),
                            'type' => 'expense',
                            'amount' => rand(200000, 800000), // 200k - 800k
                            'notes' => 'Pengeluaran operasional FnB',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                }

                // VOO Transactions (Viera Oleh-oleh)
                if ($vooCategory) {
                    // Revenue VOO (penjualan oleh-oleh)
                    $transactions[] = [
                        'outlet_id' => $outlet->id,
                        'category_id' => $vooCategory->id,
                        'transaction_date' => $date->format('Y-m-d'),
                        'description' => $this->getVOORevenueDescription(),
                        'type' => 'revenue',
                        'amount' => rand(300000, 1500000), // 300k - 1.5jt
                        'notes' => 'Penjualan oleh-oleh harian',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    // Expense VOO (restocking, packaging)
                    if (rand(1, 4) == 1) { // Lebih jarang expense untuk oleh-oleh
                        $transactions[] = [
                            'outlet_id' => $outlet->id,
                            'category_id' => $vooCategory->id,
                            'transaction_date' => $date->format('Y-m-d'),
                            'description' => $this->getVOOExpenseDescription(),
                            'type' => 'expense',
                            'amount' => rand(150000, 600000), // 150k - 600k
                            'notes' => 'Pengeluaran operasional VOO',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                }

                // Occasional receivables dan cash deficit
                if (rand(1, 10) == 1) { // 10% chance
                    $category = rand(1, 2) == 1 ? $fnbCategory : $vooCategory;
                    $transactions[] = [
                        'outlet_id' => $outlet->id,
                        'category_id' => $category->id,
                        'transaction_date' => $date->format('Y-m-d'),
                        'description' => 'Piutang pelanggan korporat',
                        'type' => 'receivable',
                        'amount' => rand(100000, 500000),
                        'notes' => 'Piutang dari pelanggan korporat',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                if (rand(1, 15) == 1) { // 6.7% chance
                    $category = rand(1, 2) == 1 ? $fnbCategory : $vooCategory;
                    $transactions[] = [
                        'outlet_id' => $outlet->id,
                        'category_id' => $category->id,
                        'transaction_date' => $date->format('Y-m-d'),
                        'description' => 'Kekurangan kas harian',
                        'type' => 'cash_deficit',
                        'amount' => rand(50000, 200000),
                        'notes' => 'Selisih kas dengan sistem',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }

        // Insert in chunks untuk performa yang lebih baik
        $chunks = array_chunk($transactions, 500);
        foreach ($chunks as $chunk) {
            DailyTransaction::insert($chunk);
        }

        $this->command->info('Generated ' . count($transactions) . ' daily transactions');
    }

    private function getFnBRevenueDescription(): string
    {
        $descriptions = [
            'Penjualan makanan dan minuman',
            'Revenue dari menu utama',
            'Penjualan paket combo',
            'Revenue minuman dan snack',
            'Penjualan menu spesial',
            'Revenue catering',
            'Penjualan menu breakfast',
            'Revenue lunch dan dinner',
        ];

        return $descriptions[array_rand($descriptions)];
    }

    private function getFnBExpenseDescription(): string
    {
        $descriptions = [
            'Pembelian bahan baku makanan',
            'Restocking minuman',
            'Biaya gas dan listrik dapur',
            'Pembelian bumbu dan seasoning',
            'Biaya packaging makanan',
            'Maintenance peralatan dapur',
            'Pembelian sayuran segar',
            'Biaya cleaning supplies',
        ];

        return $descriptions[array_rand($descriptions)];
    }

    private function getVOORevenueDescription(): string
    {
        $descriptions = [
            'Penjualan oleh-oleh khas daerah',
            'Revenue souvenir dan kerajinan',
            'Penjualan makanan kemasan',
            'Revenue produk lokal',
            'Penjualan merchandise Viera',
            'Revenue gift set oleh-oleh',
            'Penjualan produk eksklusif',
            'Revenue paket wisata kuliner',
        ];

        return $descriptions[array_rand($descriptions)];
    }

    private function getVOOExpenseDescription(): string
    {
        $descriptions = [
            'Restocking produk oleh-oleh',
            'Pembelian packaging premium',
            'Biaya display dan etalase',
            'Maintenance area penjualan',
            'Biaya promosi produk',
            'Pembelian produk dari supplier',
            'Biaya labeling dan branding',
            'Maintenance sistem POS',
        ];

        return $descriptions[array_rand($descriptions)];
    }
}
