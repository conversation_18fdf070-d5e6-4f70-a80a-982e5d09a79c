<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TransactionCategory;
use App\Models\TransactionSubcategory;

class TransactionSubcategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subcategories = [
            // Beban Bahan Baku
            ['category_code' => 'beban_bahan_baku', 'code' => 'tagihan_rkv', 'name' => 'Tagihan RKV'],
            ['category_code' => 'beban_bahan_baku', 'code' => 'tagihan_mitra', 'name' => 'Tagih<PERSON> Mitra'],
            ['category_code' => 'beban_bahan_baku', 'code' => 'tagihan_supplier', 'name' => 'Tagihan Supplier'],
            ['category_code' => 'beban_bahan_baku', 'code' => 'bahan_baku_lainnya', 'name' => 'Bahan Baku Lainnya'],

            // Beban GA
            ['category_code' => 'beban_ga', 'code' => 'belanja_ga', 'name' => 'Belanja GA'],
            ['category_code' => 'beban_ga', 'code' => 'belanja_aset_ga', 'name' => 'Belanja Aset GA'],
            ['category_code' => 'beban_ga', 'code' => 'harau_sticker_banner', 'name' => 'Harau Sticker/Banner'],
            ['category_code' => 'beban_ga', 'code' => 'material_bangunan', 'name' => 'Material Bangunan/Kabel/Bayar Tukang/Instalasi/Kasbon Tukang'],
            ['category_code' => 'beban_ga', 'code' => 'service_equipment', 'name' => 'Service Freezer/Mobil/AC/Motor/CCTV/dll'],
            ['category_code' => 'beban_ga', 'code' => 'kertas_thermal', 'name' => 'Kertas Thermal/Kertas Label/Nota'],
            ['category_code' => 'beban_ga', 'code' => 'service_kompor_freezer', 'name' => 'Service Kompor dan Freezer/Mobil/AC/Motor'],
            ['category_code' => 'beban_ga', 'code' => 'bensin_luxio', 'name' => 'Bensin Luxio Baru'],
            ['category_code' => 'beban_ga', 'code' => 'konten_video_foto', 'name' => 'Konten Video/Foto Produk'],
            ['category_code' => 'beban_ga', 'code' => 'lppom_mui', 'name' => 'Biaya Pengurusan LPPOM MUI RIAU'],
            ['category_code' => 'beban_ga', 'code' => 'konsumsi_meeting', 'name' => 'Konsumsi Meeting Direktur/Chef Deden'],
            ['category_code' => 'beban_ga', 'code' => 'ga_lainnya', 'name' => 'GA Lainnya'],

            // Beban Promosi
            ['category_code' => 'beban_promosi', 'code' => 'free_ultah', 'name' => 'Free Ultah'],
            ['category_code' => 'beban_promosi', 'code' => 'free_bundling', 'name' => 'Free Paket Bundling'],
            ['category_code' => 'beban_promosi', 'code' => 'marketing_crm', 'name' => 'Pengeluaran Marketing/CRM'],
            ['category_code' => 'beban_promosi', 'code' => 'free_talam', 'name' => 'Free Talam'],
            ['category_code' => 'beban_promosi', 'code' => 'tester', 'name' => 'Tester'],
            ['category_code' => 'beban_promosi', 'code' => 'gift_card', 'name' => 'Gift Card'],
            ['category_code' => 'beban_promosi', 'code' => 'promosi_lainnya', 'name' => 'Promosi Lainnya'],

            // Beban Utilitas
            ['category_code' => 'beban_utilitas', 'code' => 'listrik', 'name' => 'Bayar Listrik'],
            ['category_code' => 'beban_utilitas', 'code' => 'internet_pulsa', 'name' => 'Bayar Indihome/Pulsa/Paket Telepon'],
            ['category_code' => 'beban_utilitas', 'code' => 'pest_control', 'name' => 'Jasa Pengendalian Hama (Petsco)'],
            ['category_code' => 'beban_utilitas', 'code' => 'kebersihan', 'name' => 'Uang Kebersihan/Uang Ronda/PDAM'],
            ['category_code' => 'beban_utilitas', 'code' => 'air_pdam', 'name' => 'Air PDAM'],
            ['category_code' => 'beban_utilitas', 'code' => 'gas_lpg', 'name' => 'Gas LPG'],
            ['category_code' => 'beban_utilitas', 'code' => 'utilitas_lainnya', 'name' => 'Utilitas Lainnya'],

            // Pajak
            ['category_code' => 'pajak', 'code' => 'pajak_bapenda', 'name' => 'Bayar Pajak/Bapenda/Fee Pajak'],
            ['category_code' => 'pajak', 'code' => 'pph', 'name' => 'PPh'],
            ['category_code' => 'pajak', 'code' => 'ppn', 'name' => 'PPN'],
            ['category_code' => 'pajak', 'code' => 'pbb', 'name' => 'PBB'],
            ['category_code' => 'pajak', 'code' => 'pajak_lainnya', 'name' => 'Pajak Lainnya'],

            // Ongkir
            ['category_code' => 'ongkir', 'code' => 'ongkir_customer', 'name' => 'Ongkir Customer'],
            ['category_code' => 'ongkir', 'code' => 'ongkir_cabang', 'name' => 'Ongkir ke Cabang'],
            ['category_code' => 'ongkir', 'code' => 'ongkir_lainnya', 'name' => 'Ongkir Lainnya'],

            // BPJS
            ['category_code' => 'bpjs', 'code' => 'bpjs_kesehatan', 'name' => 'BPJS Kesehatan'],
            ['category_code' => 'bpjs', 'code' => 'bpjs_tk', 'name' => 'Bayar BPJS TK'],

            // Komisi Bank
            ['category_code' => 'komisi_bank', 'code' => 'komisi_bank_gojek', 'name' => 'Komisi Bank dan Gojek (Debit,QR,Gojek)'],
            ['category_code' => 'komisi_bank', 'code' => 'komisi_debit', 'name' => 'Komisi Debit'],
            ['category_code' => 'komisi_bank', 'code' => 'komisi_qr', 'name' => 'Komisi QR'],
            ['category_code' => 'komisi_bank', 'code' => 'komisi_gojek', 'name' => 'Komisi Gojek'],
            ['category_code' => 'komisi_bank', 'code' => 'komisi_grab', 'name' => 'Komisi Grab'],
            ['category_code' => 'komisi_bank', 'code' => 'komisi_lainnya', 'name' => 'Komisi Lainnya'],

            // Gaji
            ['category_code' => 'gaji', 'code' => 'gaji_karyawan', 'name' => 'Gaji Karyawan'],
            ['category_code' => 'gaji', 'code' => 'gaji_part_time', 'name' => 'Gaji Part Time/Freelance'],
            ['category_code' => 'gaji', 'code' => 'gaji_tetap', 'name' => 'Gaji Tetap'],
            ['category_code' => 'gaji', 'code' => 'gaji_harian', 'name' => 'Gaji Harian'],
            ['category_code' => 'gaji', 'code' => 'bonus', 'name' => 'Bonus'],
            ['category_code' => 'gaji', 'code' => 'tunjangan', 'name' => 'Tunjangan'],
            ['category_code' => 'gaji', 'code' => 'gaji_lainnya', 'name' => 'Gaji Lainnya'],

            // Other
            ['category_code' => 'other', 'code' => 'pengeluaran_point', 'name' => 'Pengeluaran Point'],
            ['category_code' => 'other', 'code' => 'denda_keterlambatan', 'name' => 'Denda Keterlambatan'],
            ['category_code' => 'other', 'code' => 'biaya_administrasi', 'name' => 'Biaya Administrasi Bank'],
            ['category_code' => 'other', 'code' => 'donasi_csr', 'name' => 'Donasi/CSR'],
            ['category_code' => 'other', 'code' => 'training_karyawan', 'name' => 'Training Karyawan'],
            ['category_code' => 'other', 'code' => 'lain_lain', 'name' => 'Lain-lain'],
        ];

        foreach ($subcategories as $subcategoryData) {
            $category = TransactionCategory::where('code', $subcategoryData['category_code'])->first();
            if ($category) {
                TransactionSubcategory::updateOrCreate(
                    ['code' => $subcategoryData['code']],
                    [
                        'category_id' => $category->id,
                        'name' => $subcategoryData['name'],
                        'description' => 'Sub kategori untuk ' . $category->name,
                        'is_active' => true,
                    ]
                );
            }
        }

        $this->command->info('Transaction subcategories created successfully!');
    }
}
