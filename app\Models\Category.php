<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function dailyTransactions()
    {
        return $this->hasMany(DailyTransaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFnB($query)
    {
        return $query->where('name', 'FnB');
    }

    public function scopeVOO($query)
    {
        return $query->where('name', 'VOO');
    }
}
