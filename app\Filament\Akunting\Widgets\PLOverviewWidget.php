<?php

namespace App\Filament\Akunting\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class PLOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Data bulan ini
        $currentMonth = Carbon::now();
        $lastMonth = Carbon::now()->subMonth();

        // Current month data
        $currentRevenue = DailyTransaction::revenue()
            ->whereYear('transaction_date', $currentMonth->year)
            ->whereMonth('transaction_date', $currentMonth->month)
            ->sum('amount');

        $currentExpense = DailyTransaction::expense()
            ->whereYear('transaction_date', $currentMonth->year)
            ->whereMonth('transaction_date', $currentMonth->month)
            ->sum('amount');

        $currentProfit = $currentRevenue - $currentExpense;

        // Last month data for comparison
        $lastRevenue = DailyTransaction::revenue()
            ->whereYear('transaction_date', $lastMonth->year)
            ->whereMonth('transaction_date', $lastMonth->month)
            ->sum('amount');

        $lastExpense = DailyTransaction::expense()
            ->whereYear('transaction_date', $lastMonth->year)
            ->whereMonth('transaction_date', $lastMonth->month)
            ->sum('amount');

        $lastProfit = $lastRevenue - $lastExpense;

        // Calculate percentage changes
        $revenueChange = $lastRevenue > 0 ? (($currentRevenue - $lastRevenue) / $lastRevenue) * 100 : 0;
        $expenseChange = $lastExpense > 0 ? (($currentExpense - $lastExpense) / $lastExpense) * 100 : 0;
        $profitChange = $lastProfit != 0 ? (($currentProfit - $lastProfit) / abs($lastProfit)) * 100 : 0;

        return [
            Stat::make('Total Revenue (Bulan Ini)', 'Rp ' . number_format($currentRevenue, 0, ',', '.'))
                ->description(($revenueChange >= 0 ? '+' : '') . number_format($revenueChange, 1) . '% dari bulan lalu')
                ->descriptionIcon($revenueChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($revenueChange >= 0 ? 'success' : 'danger')
                ->chart($this->getRevenueChart()),

            Stat::make('Total Expense (Bulan Ini)', 'Rp ' . number_format($currentExpense, 0, ',', '.'))
                ->description(($expenseChange >= 0 ? '+' : '') . number_format($expenseChange, 1) . '% dari bulan lalu')
                ->descriptionIcon($expenseChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($expenseChange <= 0 ? 'success' : 'danger')
                ->chart($this->getExpenseChart()),

            Stat::make('Net Profit (Bulan Ini)', 'Rp ' . number_format($currentProfit, 0, ',', '.'))
                ->description(($profitChange >= 0 ? '+' : '') . number_format($profitChange, 1) . '% dari bulan lalu')
                ->descriptionIcon($profitChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($currentProfit >= 0 ? 'success' : 'danger')
                ->chart($this->getProfitChart()),
        ];
    }

    private function getRevenueChart(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $revenue = DailyTransaction::revenue()
                ->whereDate('transaction_date', $date)
                ->sum('amount');
            $data[] = $revenue / 1000; // Convert to thousands for better chart display
        }
        return $data;
    }

    private function getExpenseChart(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $expense = DailyTransaction::expense()
                ->whereDate('transaction_date', $date)
                ->sum('amount');
            $data[] = $expense / 1000; // Convert to thousands for better chart display
        }
        return $data;
    }

    private function getProfitChart(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $revenue = DailyTransaction::revenue()
                ->whereDate('transaction_date', $date)
                ->sum('amount');
            $expense = DailyTransaction::expense()
                ->whereDate('transaction_date', $date)
                ->sum('amount');
            $profit = $revenue - $expense;
            $data[] = $profit / 1000; // Convert to thousands for better chart display
        }
        return $data;
    }
}
