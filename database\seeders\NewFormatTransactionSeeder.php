<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use Carbon\Carbon;

class NewFormatTransactionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Clear existing data
        DailyTransaction::truncate();

        // Ensure outlets exist
        $this->createOutlets();

        $outlets = Outlet::all();
        $currentMonth = Carbon::now();

        foreach ($outlets as $outlet) {
            $this->createTransactionsForOutlet($outlet, $currentMonth);
        }

        $this->command->info('New format transaction data created successfully!');
    }

    private function createOutlets()
    {
        $outlets = [
            [
                'name' => 'Viera Oleh-oleh Cabang Sudirman',
                'category' => 'VOO',
                'address' => 'Jalan Sudirman No. 123',
                'description' => 'Cabang Sudirman - Flagship Store',
                'is_active' => true,
            ],
            [
                'name' => 'VOO 1',
                'category' => 'VOO',
                'address' => 'Jalan Riau No. 45',
                'description' => 'VOO Outlet 1 - Premium Location',
                'is_active' => true,
            ],
            [
                'name' => 'VOO 2',
                'category' => 'VOO',
                'address' => 'Jalan Panam No. 67',
                'description' => 'VOO Outlet 2 - Strategic Location',
                'is_active' => true,
            ],
        ];

        foreach ($outlets as $outletData) {
            Outlet::firstOrCreate(
                ['name' => $outletData['name']],
                $outletData
            );
        }
    }

    private function createTransactionsForOutlet(Outlet $outlet, Carbon $month)
    {
        // Revenue transactions with new format
        $this->createRevenueTransactions($outlet, $month);

        // Expense transactions with detailed subcategories
        $this->createExpenseTransactions($outlet, $month);
    }

    private function createRevenueTransactions(Outlet $outlet, Carbon $month)
    {
        $revenueMultiplier = $this->getOutletMultiplier($outlet->name);

        $revenues = [
            ['payment_method' => 'pendapatan_cash', 'base_amount' => 400000000],
            ['payment_method' => 'pendapatan_debit', 'base_amount' => ********0],
            ['payment_method' => 'pendapatan_transfer', 'base_amount' => 80000000],
            ['payment_method' => 'pendapatan_qris', 'base_amount' => 200000000],
            ['payment_method' => 'pendapatan_gojek', 'base_amount' => 45000000],
            ['payment_method' => 'pendapatan_grab_ovo', 'base_amount' => 8000000],
            ['payment_method' => 'pendapatan_sewa_rak', 'base_amount' => 2000000],
        ];

        foreach ($revenues as $revenue) {
            $amount = $revenue['base_amount'] * $revenueMultiplier;
            $description = ucwords(str_replace(['pendapatan_', '_'], ['', ' '], $revenue['payment_method']));

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->subDays(rand(1, 28)),
                'description' => $description,
                'type' => 'revenue',
                'payment_method' => $revenue['payment_method'],
                'amount' => $amount + rand(-$amount * 0.1, $amount * 0.1), // Add 10% variance
                'notes' => "Revenue data for {$outlet->name} - " . $month->format('F Y'),
            ]);
        }
    }

    private function createExpenseTransactions(Outlet $outlet, Carbon $month)
    {
        $expenseMultiplier = $this->getOutletMultiplier($outlet->name);

        $expenses = [
            // Beban Bahan Baku
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_rkv', 'base_amount' => ********0],
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_mitra', 'base_amount' => 200000000],
            ['category' => 'beban_bahan_baku', 'subcategory' => 'tagihan_supplier', 'base_amount' => 80000000],

            // Beban GA dengan subcategory detail
            ['category' => 'beban_ga', 'subcategory' => 'material_bangunan', 'base_amount' => 2000000],
            ['category' => 'beban_ga', 'subcategory' => 'service_oven_freezer_packing', 'base_amount' => 1500000],
            ['category' => 'beban_ga', 'subcategory' => 'service_equipment', 'base_amount' => 1200000],
            ['category' => 'beban_ga', 'subcategory' => 'belanja_ga', 'base_amount' => 800000],
            ['category' => 'beban_ga', 'subcategory' => 'cuci_mobil_oli', 'base_amount' => 600000],
            ['category' => 'beban_ga', 'subcategory' => 'kertas_thermal', 'base_amount' => 500000],
            ['category' => 'beban_ga', 'subcategory' => 'keperluan_genset', 'base_amount' => 900000],
            ['category' => 'beban_ga', 'subcategory' => 'bensin_luxio_putih', 'base_amount' => 2500000],
            ['category' => 'beban_ga', 'subcategory' => 'bensin_luxio_silver', 'base_amount' => 2800000],

            // Beban Promosi dengan detail
            ['category' => 'beban_promosi', 'subcategory' => 'free_talam_rs', 'base_amount' => 1200000],
            ['category' => 'beban_promosi', 'subcategory' => 'free_gift_ultah', 'base_amount' => 900000],
            ['category' => 'beban_promosi', 'subcategory' => 'kue_marketing', 'base_amount' => 700000],
            ['category' => 'beban_promosi', 'subcategory' => 'tester', 'base_amount' => 600000],
            ['category' => 'beban_promosi', 'subcategory' => 'free_bundling_kuker', 'base_amount' => 500000],
            ['category' => 'beban_promosi', 'subcategory' => 'gift_card', 'base_amount' => 100000],

            // Beban Utilitas
            ['category' => 'beban_utilitas', 'subcategory' => 'listrik', 'base_amount' => 8000000],
            ['category' => 'beban_utilitas', 'subcategory' => 'internet_pulsa', 'base_amount' => 400000],
            ['category' => 'beban_utilitas', 'subcategory' => 'pest_control', 'base_amount' => 700000],
            ['category' => 'beban_utilitas', 'subcategory' => 'kebersihan', 'base_amount' => 200000],

            // Pajak
            ['category' => 'pajak', 'subcategory' => 'pajak_bapenda', 'base_amount' => 5000000],

            // BPJS
            ['category' => 'bpjs', 'subcategory' => 'bpjs_kesehatan', 'base_amount' => 800000],
            ['category' => 'bpjs', 'subcategory' => 'bpjs_tk', 'base_amount' => 200000],

            // Ongkir
            ['category' => 'ongkir', 'subcategory' => 'ongkir_customer_refund', 'base_amount' => 1000000],
            ['category' => 'ongkir', 'subcategory' => 'fee_supir_bus', 'base_amount' => 300000],
            ['category' => 'ongkir', 'subcategory' => 'ongkir_cabang', 'base_amount' => 250000],

            // Komisi Bank
            ['category' => 'komisi_bank', 'subcategory' => 'komisi_bank_gojek', 'base_amount' => ********],

            // Gaji
            ['category' => 'gaji', 'subcategory' => 'gaji_karyawan', 'base_amount' => ********],
            ['category' => 'gaji', 'subcategory' => 'gaji_part_time', 'base_amount' => 8000000],

            // Other
            ['category' => 'other', 'subcategory' => 'pengeluaran_point', 'base_amount' => 300000],
        ];

        foreach ($expenses as $expense) {
            $amount = $expense['base_amount'] * $expenseMultiplier;
            $description = $this->getExpenseDescription($expense['subcategory']);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->subDays(rand(1, 28)),
                'description' => $description,
                'type' => 'expense',
                'expense_category' => $expense['category'],
                'subcategory' => $expense['subcategory'],
                'amount' => $amount + rand(-$amount * 0.15, $amount * 0.15), // Add 15% variance
                'notes' => "Expense data for {$outlet->name} - " . $month->format('F Y'),
            ]);
        }
    }

    private function getOutletMultiplier(string $outletName): float
    {
        return match ($outletName) {
            'Viera Oleh-oleh Cabang Sudirman' => 1.2, // 20% higher
            'VOO 1' => 1.0, // Base
            'VOO 2' => 0.8, // 20% lower
            default => 1.0,
        };
    }

    private function getExpenseDescription(string $subcategory): string
    {
        $descriptions = [
            'tagihan_rkv' => 'Tagihan RKV',
            'tagihan_mitra' => 'Tagihan Mitra',
            'tagihan_supplier' => 'Tagihan Supplier',
            'material_bangunan' => 'Material Bangunan/Kabel/Bayar Tukang',
            'service_oven_freezer_packing' => 'Service Oven dan Freezer/Mobil/AC/Mesin Packing/Genset',
            'service_equipment' => 'Service Freezer/Mobil/AC/Motor/CCTV/dll',
            'belanja_ga' => 'Belanja GA',
            'cuci_mobil_oli' => 'Cuci Mobil/Isi Angin/Tambal Ban/Ganti Oli',
            'kertas_thermal' => 'Kertas Thermal/Kertas Label',
            'keperluan_genset' => 'Keperluan Genset/Dexlite Genset/cas aki genset',
            'bensin_luxio_putih' => 'Bensin Luxio Putih',
            'bensin_luxio_silver' => 'Bensin Luxio Silver',
            'free_talam_rs' => 'Free talam RS. annisa',
            'free_gift_ultah' => 'Free gift ultah',
            'kue_marketing' => 'kue keperluan marketing/Pengeluaran Marketing',
            'tester' => 'Tester',
            'free_bundling_kuker' => 'Free Bundling Kuker',
            'gift_card' => 'Gift Card',
            'listrik' => 'Bayar Listrik',
            'internet_pulsa' => 'Bayar Indihome/Pulsa/Paket Telepon',
            'pest_control' => 'Jasa Pengendalian Hama (Petsco)',
            'kebersihan' => 'Uang Kebersihan (Angkut Sampah)/Uang Ronda/PDAM',
            'pajak_bapenda' => 'Bayar PPh/PPN/PBB',
            'bpjs_kesehatan' => 'BPJS Kesehatan',
            'bpjs_tk' => 'BPJS TK',
            'ongkir_customer_refund' => 'Ongkir Customer/Refund',
            'fee_supir_bus' => 'Fee supir bus',
            'ongkir_cabang' => 'Ongkir ke cabang',
            'komisi_bank_gojek' => 'Komisi Bank dan Gojek (debit,qr,gojek,grab)',
            'gaji_karyawan' => 'Gaji karyawan',
            'gaji_part_time' => 'Gaji Part Time/Freelance',
            'pengeluaran_point' => 'Pengeluaran Point',
        ];

        return $descriptions[$subcategory] ?? ucwords(str_replace('_', ' ', $subcategory));
    }
}
