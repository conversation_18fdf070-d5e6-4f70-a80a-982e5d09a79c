<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use App\Models\Outlet;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class MonthlyOutletReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Monthly Outlet Report';

    protected static ?string $title = 'Monthly Outlet Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static string $view = 'filament.akunting.pages.monthly-outlet-report';

    public ?array $data = [];
    public $selectedOutlet = null;
    public $selectedMonth = null;
    public $selectedYear = null;
    public $reportData = [];

    public function mount(): void
    {
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->form->fill([
            'outlet_id' => null,
            'month' => $this->selectedMonth,
            'year' => $this->selectedYear,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('outlet_id')
                    ->label('Outlet')
                    ->options(Outlet::where('is_active', true)->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->selectedOutlet = $state),
                Select::make('month')
                    ->label('Month')
                    ->options([
                        1 => 'January',
                        2 => 'February',
                        3 => 'March',
                        4 => 'April',
                        5 => 'May',
                        6 => 'June',
                        7 => 'July',
                        8 => 'August',
                        9 => 'September',
                        10 => 'October',
                        11 => 'November',
                        12 => 'December'
                    ])
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->selectedMonth = $state),
                Select::make('year')
                    ->label('Year')
                    ->options(collect(range(date('Y') - 5, date('Y') + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->selectedYear = $state),
            ])
            ->statePath('data')
            ->columns(3);
    }

    public function generateReport()
    {
        $data = $this->form->getState();

        if (!$data['outlet_id'] || !$data['month'] || !$data['year']) {
            return;
        }

        $this->selectedOutlet = $data['outlet_id'];
        $this->selectedMonth = $data['month'];
        $this->selectedYear = $data['year'];

        $this->reportData = $this->getReportData();
    }

    protected function getReportData(): array
    {
        if (!$this->selectedOutlet || !$this->selectedMonth || !$this->selectedYear) {
            return [];
        }

        $transactions = DailyTransaction::forOutlet($this->selectedOutlet)
            ->forMonth($this->selectedYear, $this->selectedMonth)
            ->with(['outlet', 'karyawan'])
            ->get();

        // Handle case when no transactions found
        if ($transactions->isEmpty()) {
            return [
                'outlet' => Outlet::find($this->selectedOutlet),
                'period' => Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('F Y'),
                'summary' => [
                    'total_revenue' => 0,
                    'total_expense' => 0,
                    'total_receivable' => 0,
                    'total_cash_deficit' => 0,
                    'laba_kotor' => 0,
                    'total_beban_operasional' => 0,
                    'laba_bersih' => 0,
                ],
                'revenue_breakdown' => [],
                'expense_breakdown' => [],
                'by_category' => [],
                'transactions' => collect(),
                'has_data' => false,
            ];
        }

        // Revenue breakdown by payment method (support both old and new format)
        $revenueBreakdown = [
            'cash' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['cash', 'pendapatan_cash'])->sum('amount'),
            'debit' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['debit', 'pendapatan_debit'])->sum('amount'),
            'transfer' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['transfer', 'pendapatan_transfer'])->sum('amount'),
            'qris' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['qris', 'pendapatan_qris'])->sum('amount'),
            'gojek' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['gojek', 'pendapatan_gojek'])->sum('amount'),
            'grab_ovo' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['grab_ovo', 'pendapatan_grab_ovo'])->sum('amount'),
            'sewa_rak' => $transactions->where('type', 'revenue')->whereIn('payment_method', ['sewa_rak', 'pendapatan_sewa_rak'])->sum('amount'),
            'other' => $transactions->where('type', 'revenue')->where('payment_method', 'other')->sum('amount'),
        ];

        // Expense breakdown by category with subcategories
        $expenseBreakdown = [
            'beban_bahan_baku' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'beban_bahan_baku')->sum('amount'),
                'details' => [
                    'tagihan_rkv' => $transactions->where('type', 'expense')->where('subcategory', 'tagihan_rkv')->sum('amount'),
                    'tagihan_mitra' => $transactions->where('type', 'expense')->where('subcategory', 'tagihan_mitra')->sum('amount'),
                    'tagihan_supplier' => $transactions->where('type', 'expense')->where('subcategory', 'tagihan_supplier')->sum('amount'),
                    'bahan_baku_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'bahan_baku_lainnya')->sum('amount'),
                ]
            ],
            'beban_ga' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'beban_ga')->sum('amount'),
                'details' => [
                    'material_bangunan' => $transactions->where('type', 'expense')->where('subcategory', 'material_bangunan')->sum('amount'),
                    'kertas_thermal' => $transactions->where('type', 'expense')->where('subcategory', 'kertas_thermal')->sum('amount'),
                    'belanja_ga' => $transactions->where('type', 'expense')->where('subcategory', 'belanja_ga')->sum('amount'),
                    'bensin' => $transactions->where('type', 'expense')->where('subcategory', 'bensin')->sum('amount'),
                    'keperluan_genset' => $transactions->where('type', 'expense')->where('subcategory', 'keperluan_genset')->sum('amount'),
                    'alat_tulis' => $transactions->where('type', 'expense')->where('subcategory', 'alat_tulis')->sum('amount'),
                    'peralatan_dapur' => $transactions->where('type', 'expense')->where('subcategory', 'peralatan_dapur')->sum('amount'),
                    'seragam_karyawan' => $transactions->where('type', 'expense')->where('subcategory', 'seragam_karyawan')->sum('amount'),
                    'ga_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'ga_lainnya')->sum('amount'),
                ]
            ],
            'beban_promosi' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'beban_promosi')->sum('amount'),
                'details' => [
                    'free_ultah' => $transactions->where('type', 'expense')->where('subcategory', 'free_ultah')->sum('amount'),
                    'free_bundling' => $transactions->where('type', 'expense')->where('subcategory', 'free_bundling')->sum('amount'),
                    'marketing_crm' => $transactions->where('type', 'expense')->where('subcategory', 'marketing_crm')->sum('amount'),
                    'free_talam' => $transactions->where('type', 'expense')->where('subcategory', 'free_talam')->sum('amount'),
                    'tester' => $transactions->where('type', 'expense')->where('subcategory', 'tester')->sum('amount'),
                    'gift_card' => $transactions->where('type', 'expense')->where('subcategory', 'gift_card')->sum('amount'),
                    'promosi_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'promosi_lainnya')->sum('amount'),
                ]
            ],
            'beban_utilitas' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'beban_utilitas')->sum('amount'),
                'details' => [
                    'listrik' => $transactions->where('type', 'expense')->where('subcategory', 'listrik')->sum('amount'),
                    'internet_pulsa' => $transactions->where('type', 'expense')->where('subcategory', 'internet_pulsa')->sum('amount'),
                    'pest_control' => $transactions->where('type', 'expense')->where('subcategory', 'pest_control')->sum('amount'),
                    'kebersihan' => $transactions->where('type', 'expense')->where('subcategory', 'kebersihan')->sum('amount'),
                    'air_pdam' => $transactions->where('type', 'expense')->where('subcategory', 'air_pdam')->sum('amount'),
                    'gas_lpg' => $transactions->where('type', 'expense')->where('subcategory', 'gas_lpg')->sum('amount'),
                    'maintenance_ac' => $transactions->where('type', 'expense')->where('subcategory', 'maintenance_ac')->sum('amount'),
                    'maintenance_equipment' => $transactions->where('type', 'expense')->where('subcategory', 'maintenance_equipment')->sum('amount'),
                    'utilitas_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'utilitas_lainnya')->sum('amount'),
                ]
            ],
            'pajak' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'pajak')->sum('amount'),
                'details' => [
                    'pph' => $transactions->where('type', 'expense')->where('subcategory', 'pph')->sum('amount'),
                    'ppn' => $transactions->where('type', 'expense')->where('subcategory', 'ppn')->sum('amount'),
                    'pbb' => $transactions->where('type', 'expense')->where('subcategory', 'pbb')->sum('amount'),
                    'pajak_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'pajak_lainnya')->sum('amount'),
                ]
            ],
            'ongkir' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'ongkir')->sum('amount'),
                'details' => [
                    'ongkir_customer' => $transactions->where('type', 'expense')->where('subcategory', 'ongkir_customer')->sum('amount'),
                    'ongkir_cabang' => $transactions->where('type', 'expense')->where('subcategory', 'ongkir_cabang')->sum('amount'),
                    'ongkir_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'ongkir_lainnya')->sum('amount'),
                ]
            ],
            'bpjs' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'bpjs')->sum('amount'),
                'details' => [
                    'bpjs_kesehatan' => $transactions->where('type', 'expense')->where('subcategory', 'bpjs_kesehatan')->sum('amount'),
                    'bpjs_tk' => $transactions->where('type', 'expense')->where('subcategory', 'bpjs_tk')->sum('amount'),
                ]
            ],
            'komisi_bank' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'komisi_bank')->sum('amount'),
                'details' => [
                    'komisi_debit' => $transactions->where('type', 'expense')->where('subcategory', 'komisi_debit')->sum('amount'),
                    'komisi_qr' => $transactions->where('type', 'expense')->where('subcategory', 'komisi_qr')->sum('amount'),
                    'komisi_gojek' => $transactions->where('type', 'expense')->where('subcategory', 'komisi_gojek')->sum('amount'),
                    'komisi_grab' => $transactions->where('type', 'expense')->where('subcategory', 'komisi_grab')->sum('amount'),
                    'komisi_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'komisi_lainnya')->sum('amount'),
                ]
            ],
            'gaji' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'gaji')->sum('amount'),
                'details' => [
                    'gaji_tetap' => $transactions->where('type', 'expense')->where('subcategory', 'gaji_tetap')->sum('amount'),
                    'gaji_harian' => $transactions->where('type', 'expense')->where('subcategory', 'gaji_harian')->sum('amount'),
                    'bonus' => $transactions->where('type', 'expense')->where('subcategory', 'bonus')->sum('amount'),
                    'tunjangan' => $transactions->where('type', 'expense')->where('subcategory', 'tunjangan')->sum('amount'),
                    'gaji_lainnya' => $transactions->where('type', 'expense')->where('subcategory', 'gaji_lainnya')->sum('amount'),
                ]
            ],
            'other' => [
                'total' => $transactions->where('type', 'expense')->where('expense_category', 'other')->sum('amount'),
                'details' => [
                    'pengeluaran_point' => $transactions->where('type', 'expense')->where('subcategory', 'pengeluaran_point')->sum('amount'),
                    'denda_keterlambatan' => $transactions->where('type', 'expense')->where('subcategory', 'denda_keterlambatan')->sum('amount'),
                    'biaya_administrasi' => $transactions->where('type', 'expense')->where('subcategory', 'biaya_administrasi')->sum('amount'),
                    'donasi_csr' => $transactions->where('type', 'expense')->where('subcategory', 'donasi_csr')->sum('amount'),
                    'training_karyawan' => $transactions->where('type', 'expense')->where('subcategory', 'training_karyawan')->sum('amount'),
                    'lain_lain' => $transactions->where('type', 'expense')->where('subcategory', 'lain_lain')->sum('amount'),
                ]
            ],
        ];

        $totalRevenue = array_sum($revenueBreakdown);
        $totalExpense = array_sum(array_column($expenseBreakdown, 'total'));
        $totalReceivable = $transactions->where('type', 'receivable')->sum('amount');
        $totalCashDeficit = $transactions->where('type', 'cash_deficit')->sum('amount');

        $labaKotor = $totalRevenue - $expenseBreakdown['beban_bahan_baku']['total'];
        $totalBebanOperasional = $totalExpense - $expenseBreakdown['beban_bahan_baku']['total'];
        $labaBersih = $labaKotor - $totalBebanOperasional;

        $summary = [
            'total_revenue' => $totalRevenue,
            'total_expense' => $totalExpense,
            'total_receivable' => $totalReceivable,
            'total_cash_deficit' => $totalCashDeficit,
            'laba_kotor' => $labaKotor,
            'total_beban_operasional' => $totalBebanOperasional,
            'laba_bersih' => $labaBersih,
        ];

        // Since this is for a single outlet, we don't need category breakdown
        // But we can show the outlet's category info
        $outlet = Outlet::find($this->selectedOutlet);
        $byCategory = [
            $outlet->category => [
                'revenue' => $transactions->where('type', 'revenue')->sum('amount'),
                'expense' => $transactions->where('type', 'expense')->sum('amount'),
                'receivable' => $transactions->where('type', 'receivable')->sum('amount'),
                'cash_deficit' => $transactions->where('type', 'cash_deficit')->sum('amount'),
            ]
        ];

        return [
            'outlet' => Outlet::find($this->selectedOutlet),
            'period' => Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('F Y'),
            'summary' => $summary,
            'revenue_breakdown' => $revenueBreakdown,
            'expense_breakdown' => $expenseBreakdown,
            'by_category' => $byCategory,
            'transactions' => $transactions,
            'has_data' => true,
        ];
    }

    /**
     * Safe percentage calculation to prevent division by zero
     */
    public function calculatePercentage($amount, $total, $decimals = 2): float
    {
        if ($total == 0 || $total == null) {
            return 0.0;
        }

        return round(($amount / $total) * 100, $decimals);
    }

    /**
     * Safe division to prevent division by zero
     */
    public function safeDivision($dividend, $divisor, $decimals = 2): float
    {
        if ($divisor == 0 || $divisor == null) {
            return 0.0;
        }

        return round($dividend / $divisor, $decimals);
    }

    /**
     * Format currency with proper handling of zero values
     */
    public function formatCurrency($amount): string
    {
        return 'Rp ' . number_format($amount ?? 0, 0, ',', '.');
    }

    /**
     * Get percentage display with proper formatting
     */
    public function getPercentageDisplay($amount, $total): string
    {
        $percentage = $this->calculatePercentage($amount, $total);
        return number_format($percentage, 2) . '%';
    }
}
