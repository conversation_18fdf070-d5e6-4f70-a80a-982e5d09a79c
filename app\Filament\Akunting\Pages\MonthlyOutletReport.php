<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use App\Models\Outlet;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class MonthlyOutletReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Monthly Outlet Report';

    protected static ?string $title = 'Monthly Outlet Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static string $view = 'filament.akunting.pages.monthly-outlet-report';

    public ?array $data = [];
    public $selectedOutlet = null;
    public $selectedMonth = null;
    public $selectedYear = null;
    public $reportData = [];

    public function mount(): void
    {
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->form->fill([
            'outlet_id' => null,
            'month' => $this->selectedMonth,
            'year' => $this->selectedYear,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('outlet_id')
                    ->label('Outlet')
                    ->options(Outlet::active()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedOutlet = $state),
                Select::make('month')
                    ->label('Month')
                    ->options([
                        1 => 'January',
                        2 => 'February',
                        3 => 'March',
                        4 => 'April',
                        5 => 'May',
                        6 => 'June',
                        7 => 'July',
                        8 => 'August',
                        9 => 'September',
                        10 => 'October',
                        11 => 'November',
                        12 => 'December'
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedMonth = $state),
                Select::make('year')
                    ->label('Year')
                    ->options(collect(range(date('Y') - 5, date('Y') + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedYear = $state),
            ])
            ->statePath('data')
            ->columns(3);
    }

    public function generateReport()
    {
        $data = $this->form->getState();

        if (!$data['outlet_id'] || !$data['month'] || !$data['year']) {
            return;
        }

        $this->selectedOutlet = $data['outlet_id'];
        $this->selectedMonth = $data['month'];
        $this->selectedYear = $data['year'];

        $this->reportData = $this->getReportData();
    }

    protected function getReportData(): array
    {
        if (!$this->selectedOutlet || !$this->selectedMonth || !$this->selectedYear) {
            return [];
        }

        $transactions = DailyTransaction::forOutlet($this->selectedOutlet)
            ->forMonth($this->selectedYear, $this->selectedMonth)
            ->with(['outlet', 'karyawan'])
            ->get();

        // Revenue breakdown by payment method
        $revenueBreakdown = [
            'cash' => $transactions->where('type', 'revenue')->where('payment_method', 'cash')->sum('amount'),
            'debit' => $transactions->where('type', 'revenue')->where('payment_method', 'debit')->sum('amount'),
            'transfer' => $transactions->where('type', 'revenue')->where('payment_method', 'transfer')->sum('amount'),
            'qris' => $transactions->where('type', 'revenue')->where('payment_method', 'qris')->sum('amount'),
            'gojek' => $transactions->where('type', 'revenue')->where('payment_method', 'gojek')->sum('amount'),
            'grab_ovo' => $transactions->where('type', 'revenue')->where('payment_method', 'grab_ovo')->sum('amount'),
            'sewa_rak' => $transactions->where('type', 'revenue')->where('payment_method', 'sewa_rak')->sum('amount'),
            'other' => $transactions->where('type', 'revenue')->where('payment_method', 'other')->sum('amount'),
        ];

        // Expense breakdown by category
        $expenseBreakdown = [
            'beban_bahan_baku' => $transactions->where('type', 'expense')->where('expense_category', 'beban_bahan_baku')->sum('amount'),
            'beban_ga' => $transactions->where('type', 'expense')->where('expense_category', 'beban_ga')->sum('amount'),
            'beban_promosi' => $transactions->where('type', 'expense')->where('expense_category', 'beban_promosi')->sum('amount'),
            'beban_utilitas' => $transactions->where('type', 'expense')->where('expense_category', 'beban_utilitas')->sum('amount'),
            'pajak' => $transactions->where('type', 'expense')->where('expense_category', 'pajak')->sum('amount'),
            'ongkir' => $transactions->where('type', 'expense')->where('expense_category', 'ongkir')->sum('amount'),
            'bpjs' => $transactions->where('type', 'expense')->where('expense_category', 'bpjs')->sum('amount'),
            'komisi_bank' => $transactions->where('type', 'expense')->where('expense_category', 'komisi_bank')->sum('amount'),
            'gaji' => $transactions->where('type', 'expense')->where('expense_category', 'gaji')->sum('amount'),
            'other' => $transactions->where('type', 'expense')->where('expense_category', 'other')->sum('amount'),
        ];

        $totalRevenue = array_sum($revenueBreakdown);
        $totalExpense = array_sum($expenseBreakdown);
        $totalReceivable = $transactions->where('type', 'receivable')->sum('amount');
        $totalCashDeficit = $transactions->where('type', 'cash_deficit')->sum('amount');

        $labaKotor = $totalRevenue - $expenseBreakdown['beban_bahan_baku'];
        $totalBebanOperasional = $totalExpense - $expenseBreakdown['beban_bahan_baku'];
        $labaBersih = $labaKotor - $totalBebanOperasional;

        $summary = [
            'total_revenue' => $totalRevenue,
            'total_expense' => $totalExpense,
            'total_receivable' => $totalReceivable,
            'total_cash_deficit' => $totalCashDeficit,
            'laba_kotor' => $labaKotor,
            'total_beban_operasional' => $totalBebanOperasional,
            'laba_bersih' => $labaBersih,
        ];

        // Since this is for a single outlet, we don't need category breakdown
        // But we can show the outlet's category info
        $outlet = Outlet::find($this->selectedOutlet);
        $byCategory = [
            $outlet->category => [
                'revenue' => $transactions->where('type', 'revenue')->sum('amount'),
                'expense' => $transactions->where('type', 'expense')->sum('amount'),
                'receivable' => $transactions->where('type', 'receivable')->sum('amount'),
                'cash_deficit' => $transactions->where('type', 'cash_deficit')->sum('amount'),
            ]
        ];

        return [
            'outlet' => Outlet::find($this->selectedOutlet),
            'period' => Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('F Y'),
            'summary' => $summary,
            'revenue_breakdown' => $revenueBreakdown,
            'expense_breakdown' => $expenseBreakdown,
            'by_category' => $byCategory,
            'transactions' => $transactions,
        ];
    }
}
