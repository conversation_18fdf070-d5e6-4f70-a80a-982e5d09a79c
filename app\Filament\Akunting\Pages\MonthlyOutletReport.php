<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use App\Models\Outlet;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class MonthlyOutletReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Monthly Outlet Report';

    protected static ?string $title = 'Monthly Outlet Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static string $view = 'filament.akunting.pages.monthly-outlet-report';

    public ?array $data = [];
    public $selectedOutlet = null;
    public $selectedMonth = null;
    public $selectedYear = null;
    public $reportData = [];

    public function mount(): void
    {
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->form->fill([
            'outlet_id' => null,
            'month' => $this->selectedMonth,
            'year' => $this->selectedYear,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('outlet_id')
                    ->label('Outlet')
                    ->options(Outlet::active()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedOutlet = $state),
                Select::make('month')
                    ->label('Month')
                    ->options([
                        1 => 'January',
                        2 => 'February',
                        3 => 'March',
                        4 => 'April',
                        5 => 'May',
                        6 => 'June',
                        7 => 'July',
                        8 => 'August',
                        9 => 'September',
                        10 => 'October',
                        11 => 'November',
                        12 => 'December'
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedMonth = $state),
                Select::make('year')
                    ->label('Year')
                    ->options(collect(range(date('Y') - 5, date('Y') + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state) => $this->selectedYear = $state),
            ])
            ->statePath('data')
            ->columns(3);
    }

    public function generateReport()
    {
        $data = $this->form->getState();

        if (!$data['outlet_id'] || !$data['month'] || !$data['year']) {
            return;
        }

        $this->selectedOutlet = $data['outlet_id'];
        $this->selectedMonth = $data['month'];
        $this->selectedYear = $data['year'];

        $this->reportData = $this->getReportData();
    }

    protected function getReportData(): array
    {
        if (!$this->selectedOutlet || !$this->selectedMonth || !$this->selectedYear) {
            return [];
        }

        $transactions = DailyTransaction::forOutlet($this->selectedOutlet)
            ->forMonth($this->selectedYear, $this->selectedMonth)
            ->with(['outlet'])
            ->get();

        $summary = [
            'total_revenue' => $transactions->where('type', 'revenue')->sum('amount'),
            'total_expense' => $transactions->where('type', 'expense')->sum('amount'),
            'total_receivable' => $transactions->where('type', 'receivable')->sum('amount'),
            'total_cash_deficit' => $transactions->where('type', 'cash_deficit')->sum('amount'),
        ];

        $summary['net_profit'] = $summary['total_revenue'] - $summary['total_expense'];

        // Since this is for a single outlet, we don't need category breakdown
        // But we can show the outlet's category info
        $outlet = Outlet::find($this->selectedOutlet);
        $byCategory = [
            $outlet->category => [
                'revenue' => $transactions->where('type', 'revenue')->sum('amount'),
                'expense' => $transactions->where('type', 'expense')->sum('amount'),
                'receivable' => $transactions->where('type', 'receivable')->sum('amount'),
                'cash_deficit' => $transactions->where('type', 'cash_deficit')->sum('amount'),
            ]
        ];

        return [
            'outlet' => Outlet::find($this->selectedOutlet),
            'period' => Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('F Y'),
            'summary' => $summary,
            'by_category' => $byCategory,
            'transactions' => $transactions,
        ];
    }
}
