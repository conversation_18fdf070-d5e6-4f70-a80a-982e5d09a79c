<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run comprehensive test seeder (includes all data including users)
        $this->call([
            ComprehensiveTestSeeder::class,
            // P&L System Seeders
            OutletSeeder::class,
            CategorySeeder::class,
            DailyTransactionSeeder::class,
        ]);
    }
}
