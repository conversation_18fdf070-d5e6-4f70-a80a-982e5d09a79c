<?php

namespace App\Filament\Akunting\Resources;

use App\Filament\Akunting\Resources\DailyTransactionResource\Pages;
use App\Models\DailyTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class DailyTransactionResource extends Resource
{
    protected static ?string $model = DailyTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Daily Transactions';

    protected static ?string $modelLabel = 'Daily Transaction';

    protected static ?string $pluralModelLabel = 'Daily Transactions';

    protected static ?string $navigationGroup = 'Transactions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('outlet_id')
                    ->relationship('outlet', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Outlet'),
                Forms\Components\DatePicker::make('transaction_date')
                    ->required()
                    ->default(now())
                    ->label('Tanggal Transaksi'),
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255)
                    ->label('Deskripsi'),
                Forms\Components\Select::make('type')
                    ->required()
                    ->options([
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                    ])
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        // Clear karyawan_id when type changes to non-applicable types
                        if (!in_array($state, ['cash_deficit', 'receivable'])) {
                            $set('karyawan_id', null);
                        }
                        // Clear payment_method for non-revenue types
                        if ($state !== 'revenue') {
                            $set('payment_method', null);
                        }
                        // Clear expense_category for non-expense types
                        if ($state !== 'expense') {
                            $set('expense_category', null);
                        }
                    })
                    ->label('Jenis Transaksi'),
                Forms\Components\Select::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'debit' => 'Debit',
                        'transfer' => 'Transfer',
                        'qris' => 'QRIS',
                        'gojek' => 'Gojek',
                        'grab_ovo' => 'Grab/OVO',
                        'sewa_rak' => 'Sewa Rak',
                        'other' => 'Lainnya',
                    ])
                    ->visible(fn(Get $get) => $get('type') === 'revenue')
                    ->required(fn(Get $get) => $get('type') === 'revenue')
                    ->label('Metode Pembayaran'),
                Forms\Components\Select::make('expense_category')
                    ->options([
                        'beban_bahan_baku' => 'Beban Bahan Baku',
                        'beban_ga' => 'Beban GA',
                        'beban_promosi' => 'Beban Promosi',
                        'beban_utilitas' => 'Beban Utilitas',
                        'pajak' => 'Pajak',
                        'ongkir' => 'Ongkir',
                        'bpjs' => 'BPJS',
                        'komisi_bank' => 'Komisi Bank',
                        'gaji' => 'Gaji Karyawan',
                        'other' => 'Lainnya',
                    ])
                    ->visible(fn(Get $get) => $get('type') === 'expense')
                    ->required(fn(Get $get) => $get('type') === 'expense')
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        // Clear subcategory when category changes
                        $set('subcategory', null);
                    })
                    ->label('Kategori Pengeluaran'),
                Forms\Components\Select::make('subcategory')
                    ->options(function (Get $get) {
                        $category = $get('expense_category');

                        return match ($category) {
                            'beban_bahan_baku' => [
                                'tagihan_rkv' => 'Tagihan RKV',
                                'tagihan_mitra' => 'Tagihan Mitra',
                                'tagihan_supplier' => 'Tagihan Supplier',
                                'bahan_baku_lainnya' => 'Bahan Baku Lainnya',
                            ],
                            'beban_ga' => [
                                'material_bangunan' => 'Material Bangunan/Kabel/Bayar Tukang/Instalasi',
                                'kertas_thermal' => 'Kertas Thermal/Kertas Label',
                                'belanja_ga' => 'Belanja GA',
                                'bensin' => 'Bensin',
                                'keperluan_genset' => 'Keperluan Genset/Dexlite Genset/Cas Aki Genset',
                                'ga_lainnya' => 'GA Lainnya',
                            ],
                            'beban_promosi' => [
                                'free_ultah' => 'Free Ultah',
                                'free_bundling' => 'Free Paket Bundling',
                                'marketing_crm' => 'Pengeluaran Marketing/CRM',
                                'free_talam' => 'Free Talam',
                                'tester' => 'Tester',
                                'gift_card' => 'Gift Card',
                                'promosi_lainnya' => 'Promosi Lainnya',
                            ],
                            'beban_utilitas' => [
                                'listrik' => 'Bayar Listrik',
                                'internet_pulsa' => 'Bayar Indihome/Pulsa/Paket Telepon',
                                'pest_control' => 'Jasa Pengendalian Hama (Petsco)',
                                'kebersihan' => 'Uang Kebersihan/Uang Ronda/PDAM',
                                'utilitas_lainnya' => 'Utilitas Lainnya',
                            ],
                            'pajak' => [
                                'pph' => 'PPh',
                                'ppn' => 'PPN',
                                'pbb' => 'PBB',
                                'pajak_lainnya' => 'Pajak Lainnya',
                            ],
                            'ongkir' => [
                                'ongkir_customer' => 'Ongkir Customer',
                                'ongkir_cabang' => 'Ongkir ke Cabang',
                                'ongkir_lainnya' => 'Ongkir Lainnya',
                            ],
                            'bpjs' => [
                                'bpjs_kesehatan' => 'BPJS Kesehatan',
                                'bpjs_tk' => 'BPJS TK',
                            ],
                            'komisi_bank' => [
                                'komisi_debit' => 'Komisi Debit',
                                'komisi_qr' => 'Komisi QR',
                                'komisi_gojek' => 'Komisi Gojek',
                                'komisi_grab' => 'Komisi Grab',
                                'komisi_lainnya' => 'Komisi Lainnya',
                            ],
                            'gaji' => [
                                'gaji_tetap' => 'Gaji Tetap',
                                'gaji_harian' => 'Gaji Harian',
                                'bonus' => 'Bonus',
                                'tunjangan' => 'Tunjangan',
                                'gaji_lainnya' => 'Gaji Lainnya',
                            ],
                            'other' => [
                                'pengeluaran_point' => 'Pengeluaran Point',
                                'lain_lain' => 'Lain-lain',
                            ],
                            default => [],
                        };
                    })
                    ->visible(fn(Get $get) => $get('type') === 'expense' && $get('expense_category'))
                    ->searchable()
                    ->label('Sub Kategori'),
                Forms\Components\Select::make('karyawan_id')
                    ->relationship('karyawan', 'nama_lengkap')
                    ->searchable()
                    ->preload()
                    ->label('Karyawan (Untuk Kekurangan Kas/Piutang)')
                    ->helperText('Pilih karyawan jika transaksi terkait dengan karyawan tertentu')
                    ->visible(function (Get $get) {
                        $type = $get('type');
                        return in_array($type, ['cash_deficit', 'receivable']);
                    })
                    ->required(function (Get $get) {
                        return $get('type') === 'cash_deficit';
                    }),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric()
                    ->prefix('Rp')
                    ->step(0.01)
                    ->label('Jumlah'),
                Forms\Components\Textarea::make('notes')
                    ->maxLength(65535)
                    ->columnSpanFull()
                    ->label('Catatan'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('outlet.name')
                    ->searchable()
                    ->sortable()
                    ->label('Outlet'),
                Tables\Columns\TextColumn::make('outlet.category')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'FnB' => 'success',
                        'VOO' => 'warning',
                        default => 'gray',
                    })
                    ->label('Kategori'),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->date()
                    ->sortable()
                    ->label('Tanggal'),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->limit(30)
                    ->label('Deskripsi'),
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->searchable()
                    ->label('Karyawan')
                    ->placeholder('—')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->searchable()
                    ->label('Metode Bayar')
                    ->placeholder('—')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'cash' => 'Cash',
                        'debit' => 'Debit',
                        'transfer' => 'Transfer',
                        'qris' => 'QRIS',
                        'gojek' => 'Gojek',
                        'grab_ovo' => 'Grab/OVO',
                        'sewa_rak' => 'Sewa Rak',
                        'other' => 'Lainnya',
                        default => $state,
                    })
                    ->toggleable(),
                Tables\Columns\TextColumn::make('expense_category')
                    ->searchable()
                    ->label('Kategori')
                    ->placeholder('—')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'beban_bahan_baku' => 'Beban Bahan Baku',
                        'beban_ga' => 'Beban GA',
                        'beban_promosi' => 'Beban Promosi',
                        'beban_utilitas' => 'Beban Utilitas',
                        'pajak' => 'Pajak',
                        'ongkir' => 'Ongkir',
                        'bpjs' => 'BPJS',
                        'komisi_bank' => 'Komisi Bank',
                        'gaji' => 'Gaji Karyawan',
                        'other' => 'Lainnya',
                        default => $state,
                    })
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subcategory')
                    ->searchable()
                    ->label('Sub Kategori')
                    ->placeholder('—')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'revenue' => 'success',
                        'expense' => 'danger',
                        'receivable' => 'warning',
                        'cash_deficit' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                        default => $state,
                    })
                    ->label('Jenis'),
                Tables\Columns\TextColumn::make('amount')
                    ->money('IDR')
                    ->sortable()
                    ->label('Jumlah'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Dibuat'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('outlet_id')
                    ->relationship('outlet', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Outlet'),
                Tables\Filters\SelectFilter::make('outlet.category')
                    ->options([
                        'FnB' => 'Food & Beverage (FnB)',
                        'VOO' => 'Viera Oleh-oleh (VOO)',
                    ])
                    ->query(function ($query, array $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->whereHas('outlet', function ($q) use ($data) {
                                $q->where('category', $data['value']);
                            });
                        }
                        return $query;
                    })
                    ->label('Kategori'),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                    ])
                    ->label('Jenis Transaksi'),
                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when($data['from'], fn($query, $date) => $query->whereDate('transaction_date', '>=', $date))
                            ->when($data['until'], fn($query, $date) => $query->whereDate('transaction_date', '<=', $date));
                    }),
                SelectFilter::make('month')
                    ->options([
                        1 => 'Januari',
                        2 => 'Februari',
                        3 => 'Maret',
                        4 => 'April',
                        5 => 'Mei',
                        6 => 'Juni',
                        7 => 'Juli',
                        8 => 'Agustus',
                        9 => 'September',
                        10 => 'Oktober',
                        11 => 'November',
                        12 => 'Desember'
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->whereMonth('transaction_date', $data['value']);
                        }
                        return $query;
                    })
                    ->default(now()->month)
                    ->label('Bulan'),
                SelectFilter::make('year')
                    ->options(function () {
                        $years = [];
                        for ($year = date('Y') - 5; $year <= date('Y') + 1; $year++) {
                            $years[$year] = $year;
                        }
                        return $years;
                    })
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->whereYear('transaction_date', $data['value']);
                        }
                        return $query;
                    })
                    ->default(now()->year)
                    ->label('Tahun'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            DailyTransactionResource\Widgets\TransactionStatsWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDailyTransactions::route('/'),
            'create' => Pages\CreateDailyTransaction::route('/create'),
            'edit' => Pages\EditDailyTransaction::route('/{record}/edit'),
        ];
    }
}
