<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'FnB',
                'description' => 'Food & Beverage - Kategori untuk semua transaksi yang berkaitan dengan makanan dan minuman',
                'is_active' => true,
            ],
            [
                'name' => 'VOO',
                'description' => 'Vehicle Operation & Others - Kategori untuk operasional kendaraan dan lainnya',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
