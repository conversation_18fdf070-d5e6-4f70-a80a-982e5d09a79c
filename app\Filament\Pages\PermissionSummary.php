<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use App\Models\Karyawan;
use App\Services\PermissionService;
use Filament\Actions\Action;

class PermissionSummary extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationLabel = 'Permission Summary';
    protected static string $view = 'filament.pages.permission-summary';
    protected static ?string $navigationGroup = 'HR Management';
    protected static ?int $navigationSort = 2;

    public ?array $data = [];
    public $selectedKaryawan = null;
    public $permissionSummary = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('karyawan_id')
                    ->label('Pilih Karyawan')
                    ->placeholder('Pilih karyawan untuk melihat permission...')
                    ->options(
                        Karyawan::where('status_aktif', true)
                            ->whereHas('permissions', function ($query) {
                                $query->where('is_active', true);
                            })
                            ->with(['entitas', 'departemen', 'divisi', 'jabatan'])
                            ->get()
                            ->mapWithKeys(function ($k) {
                                $info = "{$k->nama_lengkap} - {$k->nip}";
                                if ($k->jabatan) {
                                    $info .= " ({$k->jabatan->nama_jabatan})";
                                }
                                if ($k->entitas) {
                                    $info .= " - {$k->entitas->nama}";
                                }
                                return [$k->id => $info];
                            })
                            ->toArray()
                    )
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            $this->selectedKaryawan = Karyawan::find($state);
                            $this->permissionSummary = PermissionService::getPermissionSummary($state);
                        } else {
                            $this->selectedKaryawan = null;
                            $this->permissionSummary = [];
                        }
                    }),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_permission')
                ->label('Tambah Permission')
                ->icon('heroicon-o-plus')
                ->url(route('filament.admin.resources.karyawan-permissions.create'))
                ->color('primary'),
        ];
    }

    public function getTitle(): string
    {
        return 'Permission Summary';
    }

    public function getHeading(): string
    {
        return 'Ringkasan Permission Karyawan';
    }

    public function getSubheading(): string
    {
        return 'Lihat ringkasan permission yang dimiliki oleh karyawan. Hanya menampilkan karyawan yang memiliki permission khusus.';
    }
}
