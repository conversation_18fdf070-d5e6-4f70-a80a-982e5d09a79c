<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TransactionCategory;
use App\Models\TransactionSubcategory;

class TransactionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Revenue Categories
        $revenueCategories = [
            [
                'code' => 'revenue_cash',
                'name' => 'Pendapatan Cash',
                'type' => 'revenue',
                'description' => 'Pendapatan dari penjualan tunai',
            ],
            [
                'code' => 'revenue_digital',
                'name' => 'Pendapatan Digital',
                'type' => 'revenue',
                'description' => 'Pendapatan dari pembayaran digital',
            ],
        ];

        // Expense Categories
        $expenseCategories = [
            [
                'code' => 'beban_bahan_baku',
                'name' => 'Beban Bahan Baku',
                'type' => 'expense',
                'description' => 'Biaya untuk bahan baku dan supplier',
            ],
            [
                'code' => 'beban_ga',
                'name' => 'Beban GA (General Affairs)',
                'type' => 'expense',
                'description' => 'Biaya operasional umum',
            ],
            [
                'code' => 'beban_promosi',
                'name' => 'Beban Promosi',
                'type' => 'expense',
                'description' => 'Biaya marketing dan promosi',
            ],
            [
                'code' => 'beban_utilitas',
                'name' => 'Beban Utilitas',
                'type' => 'expense',
                'description' => 'Biaya listrik, air, internet, dll',
            ],
            [
                'code' => 'pajak',
                'name' => 'Pajak',
                'type' => 'expense',
                'description' => 'Pembayaran pajak',
            ],
            [
                'code' => 'ongkir',
                'name' => 'Ongkos Kirim',
                'type' => 'expense',
                'description' => 'Biaya pengiriman',
            ],
            [
                'code' => 'bpjs',
                'name' => 'BPJS',
                'type' => 'expense',
                'description' => 'Pembayaran BPJS',
            ],
            [
                'code' => 'komisi_bank',
                'name' => 'Komisi Bank',
                'type' => 'expense',
                'description' => 'Biaya komisi bank dan payment gateway',
            ],
            [
                'code' => 'gaji',
                'name' => 'Gaji Karyawan',
                'type' => 'expense',
                'description' => 'Pembayaran gaji dan tunjangan',
            ],
            [
                'code' => 'other',
                'name' => 'Lainnya',
                'type' => 'expense',
                'description' => 'Pengeluaran lain-lain',
            ],
        ];

        // Create categories
        foreach (array_merge($revenueCategories, $expenseCategories) as $categoryData) {
            TransactionCategory::create($categoryData);
        }

        $this->command->info('Transaction categories created successfully!');
    }
}
