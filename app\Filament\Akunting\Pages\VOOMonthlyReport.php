<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use App\Models\DailyTransaction;
use Carbon\Carbon;

class VOOMonthlyReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-gift';

    protected static ?string $navigationLabel = 'VOO Monthly Report';

    protected static ?string $title = 'Viera Oleh-oleh Monthly Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static string $view = 'filament.akunting.pages.v-o-o-monthly-report';

    public ?array $data = [];
    public $selectedMonth = null;
    public $selectedYear = null;
    public $reportData = [];

    public function mount(): void
    {
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->form->fill([
            'month' => $this->selectedMonth,
            'year' => $this->selectedYear,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('month')
                    ->label('Month')
                    ->options([
                        1 => 'January',
                        2 => 'February',
                        3 => 'March',
                        4 => 'April',
                        5 => 'May',
                        6 => 'June',
                        7 => 'July',
                        8 => 'August',
                        9 => 'September',
                        10 => 'October',
                        11 => 'November',
                        12 => 'December'
                    ])
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->selectedMonth = $state),
                Select::make('year')
                    ->label('Year')
                    ->options(collect(range(date('Y') - 5, date('Y') + 1))->mapWithKeys(fn($year) => [$year => $year]))
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn($state) => $this->selectedYear = $state),
            ])
            ->statePath('data')
            ->columns(2);
    }

    public function generateReport()
    {
        $data = $this->form->getState();

        if (!$data['month'] || !$data['year']) {
            return;
        }

        $this->selectedMonth = $data['month'];
        $this->selectedYear = $data['year'];

        $this->reportData = $this->getReportData();
    }

    protected function getReportData(): array
    {
        if (!$this->selectedMonth || !$this->selectedYear) {
            return [];
        }

        // Get VOO transactions only
        $transactions = DailyTransaction::forCategory('VOO')
            ->forMonth($this->selectedYear, $this->selectedMonth)
            ->with(['outlet'])
            ->get();

        $summary = [
            'total_revenue' => $transactions->where('type', 'revenue')->sum('amount'),
            'total_expense' => $transactions->where('type', 'expense')->sum('amount'),
            'total_receivable' => $transactions->where('type', 'receivable')->sum('amount'),
            'total_cash_deficit' => $transactions->where('type', 'cash_deficit')->sum('amount'),
        ];

        $summary['net_profit'] = $summary['total_revenue'] - $summary['total_expense'];

        // Group by outlet
        $byOutlet = $transactions->groupBy('outlet.name')->map(function ($outletTransactions) {
            return [
                'revenue' => $outletTransactions->where('type', 'revenue')->sum('amount'),
                'expense' => $outletTransactions->where('type', 'expense')->sum('amount'),
                'receivable' => $outletTransactions->where('type', 'receivable')->sum('amount'),
                'cash_deficit' => $outletTransactions->where('type', 'cash_deficit')->sum('amount'),
            ];
        });

        return [
            'period' => Carbon::create($this->selectedYear, $this->selectedMonth, 1)->format('F Y'),
            'summary' => $summary,
            'by_outlet' => $byOutlet,
            'transactions' => $transactions,
        ];
    }

    /**
     * Safe percentage calculation to prevent division by zero
     */
    public function calculatePercentage($amount, $total, $decimals = 2): float
    {
        if ($total == 0 || $total == null) {
            return 0.0;
        }

        return round(($amount / $total) * 100, $decimals);
    }

    /**
     * Safe division to prevent division by zero
     */
    public function safeDivision($dividend, $divisor, $decimals = 2): float
    {
        if ($divisor == 0 || $divisor == null) {
            return 0.0;
        }

        return round($dividend / $divisor, $decimals);
    }

    /**
     * Format currency with proper handling of zero values
     */
    public function formatCurrency($amount): string
    {
        return 'Rp ' . number_format($amount ?? 0, 0, ',', '.');
    }

    /**
     * Get percentage display with proper formatting
     */
    public function getPercentageDisplay($amount, $total): string
    {
        $percentage = $this->calculatePercentage($amount, $total);
        return number_format($percentage, 2) . '%';
    }
}
