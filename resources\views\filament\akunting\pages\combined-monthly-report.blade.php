<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Report
            </x-slot>

            <form wire:submit.prevent="generateReport">
                {{ $this->form }}

                <div class="mt-4">
                    <x-filament::button type="submit" color="primary">
                        Generate Report
                    </x-filament::button>
                </div>
            </form>
        </x-filament::section>

        <!-- Report Results -->
        @if (!empty($reportData))
            <x-filament::section>
                <x-slot name="heading">
                    Combined Report - {{ $reportData['period'] }}
                </x-slot>

                <!-- Overall Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-green-800">Total Revenue</h3>
                        <p class="text-2xl font-bold text-green-900">
                            Rp {{ number_format($reportData['summary']['total_revenue'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-red-800">Total Expense</h3>
                        <p class="text-2xl font-bold text-red-900">
                            Rp {{ number_format($reportData['summary']['total_expense'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-yellow-800">Total Receivable</h3>
                        <p class="text-2xl font-bold text-yellow-900">
                            Rp {{ number_format($reportData['summary']['total_receivable'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800">Net Profit</h3>
                        <p
                            class="text-2xl font-bold {{ $reportData['summary']['net_profit'] >= 0 ? 'text-green-900' : 'text-red-900' }}">
                            Rp {{ number_format($reportData['summary']['net_profit'], 0, ',', '.') }}
                        </p>
                    </div>
                </div>

                <!-- Category Breakdown -->
                @if (!empty($reportData['by_category']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">Breakdown by Category</h3>

                        @foreach ($reportData['by_category'] as $categoryName => $categoryData)
                            @php
                                $categoryNet = $categoryData['revenue'] - $categoryData['expense'];
                            @endphp
                            <div
                                class="mb-6 p-4 border rounded-lg {{ $categoryName === 'FnB' ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50' }}">
                                <h4
                                    class="text-lg font-medium mb-3 {{ $categoryName === 'FnB' ? 'text-green-800' : 'text-yellow-800' }}">
                                    {{ $categoryName }} Category
                                </h4>

                                <!-- Category Summary -->
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Revenue</p>
                                        <p class="text-lg font-semibold text-green-600">
                                            Rp {{ number_format($categoryData['revenue'], 0, ',', '.') }}
                                        </p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Expense</p>
                                        <p class="text-lg font-semibold text-red-600">
                                            Rp {{ number_format($categoryData['expense'], 0, ',', '.') }}
                                        </p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Receivable</p>
                                        <p class="text-lg font-semibold text-yellow-600">
                                            Rp {{ number_format($categoryData['receivable'], 0, ',', '.') }}
                                        </p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Net Profit</p>
                                        <p
                                            class="text-lg font-semibold {{ $categoryNet >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                            Rp {{ number_format($categoryNet, 0, ',', '.') }}
                                        </p>
                                    </div>
                                </div>

                                <!-- Outlets in this category -->
                                @if (!empty($categoryData['outlets']))
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Outlet</th>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Revenue</th>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Expense</th>
                                                    <th
                                                        class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                        Net Profit</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                @foreach ($categoryData['outlets'] as $outletName => $outletData)
                                                    <tr>
                                                        <td class="px-4 py-2 text-sm font-medium text-gray-900">
                                                            {{ $outletName }}</td>
                                                        <td class="px-4 py-2 text-sm text-green-600">Rp
                                                            {{ number_format($outletData['revenue'], 0, ',', '.') }}
                                                        </td>
                                                        <td class="px-4 py-2 text-sm text-red-600">Rp
                                                            {{ number_format($outletData['expense'], 0, ',', '.') }}
                                                        </td>
                                                        <td
                                                            class="px-4 py-2 text-sm font-medium {{ $outletData['net_profit'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                            Rp
                                                            {{ number_format($outletData['net_profit'], 0, ',', '.') }}
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @endif
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
