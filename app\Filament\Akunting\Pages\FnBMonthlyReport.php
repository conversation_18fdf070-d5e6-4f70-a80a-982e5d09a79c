<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;

class FnBMonthlyReport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cake';

    protected static ?string $navigationLabel = 'FnB Monthly Report';

    protected static ?string $title = 'Food & Beverage Monthly Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static string $view = 'filament.akunting.pages.fn-b-monthly-report';
}
