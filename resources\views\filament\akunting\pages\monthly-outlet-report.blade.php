<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Report
            </x-slot>

            <form wire:submit.prevent="generateReport">
                {{ $this->form }}

                <div class="mt-4">
                    <x-filament::button type="submit" color="primary">
                        Generate Report
                    </x-filament::button>
                </div>
            </form>
        </x-filament::section>

        <!-- Report Results -->
        @if (!empty($reportData))
            <x-filament::section>
                <x-slot name="heading">
                    {{ $reportData['outlet']->name }} - {{ $reportData['period'] }}
                </x-slot>

                <!-- Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-green-800">Total Revenue</h3>
                        <p class="text-2xl font-bold text-green-900">
                            Rp {{ number_format($reportData['summary']['total_revenue'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-red-800">Total Expense</h3>
                        <p class="text-2xl font-bold text-red-900">
                            Rp {{ number_format($reportData['summary']['total_expense'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-yellow-800">Total Receivable</h3>
                        <p class="text-2xl font-bold text-yellow-900">
                            Rp {{ number_format($reportData['summary']['total_receivable'], 0, ',', '.') }}
                        </p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800">Net Profit</h3>
                        <p
                            class="text-2xl font-bold {{ $reportData['summary']['net_profit'] >= 0 ? 'text-green-900' : 'text-red-900' }}">
                            Rp {{ number_format($reportData['summary']['net_profit'], 0, ',', '.') }}
                        </p>
                    </div>
                </div>

                <!-- Category Breakdown -->
                @if (!empty($reportData['by_category']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">Breakdown by Category</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Category</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Revenue</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Expense</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Receivable</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Cash Deficit</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Net</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach ($reportData['by_category'] as $categoryName => $categoryData)
                                        @php
                                            $net = $categoryData['revenue'] - $categoryData['expense'];
                                        @endphp
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    {{ $categoryName === 'FnB' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                    {{ $categoryName }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                                Rp {{ number_format($categoryData['revenue'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                Rp {{ number_format($categoryData['expense'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                Rp {{ number_format($categoryData['receivable'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                Rp {{ number_format($categoryData['cash_deficit'], 0, ',', '.') }}
                                            </td>
                                            <td
                                                class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $net >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                Rp {{ number_format($net, 0, ',', '.') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif

                <!-- Transaction Details -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Transaction Details</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Category</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Description</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach ($reportData['transactions']->sortBy('transaction_date') as $transaction)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $transaction->transaction_date->format('d/m/Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                {{ $transaction->outlet->category === 'FnB' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                {{ $transaction->outlet->category }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            {{ $transaction->description }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @switch($transaction->type)
                                                    @case('revenue') bg-green-100 text-green-800 @break
                                                    @case('expense') bg-red-100 text-red-800 @break
                                                    @case('receivable') bg-yellow-100 text-yellow-800 @break
                                                    @case('cash_deficit') bg-gray-100 text-gray-800 @break
                                                @endswitch">
                                                {{ $transaction->type_display }}
                                            </span>
                                        </td>
                                        <td
                                            class="px-6 py-4 whitespace-nowrap text-sm font-medium
                                            @switch($transaction->type)
                                                @case('revenue') text-green-600 @break
                                                @case('expense') text-red-600 @break
                                                @case('receivable') text-yellow-600 @break
                                                @case('cash_deficit') text-gray-600 @break
                                            @endswitch">
                                            Rp {{ number_format($transaction->amount, 0, ',', '.') }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
