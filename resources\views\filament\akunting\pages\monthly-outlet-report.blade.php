<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Report
            </x-slot>

            <div class="space-y-4">
                {{ $this->form }}

                <div class="mt-4">
                    <x-filament::button wire:click="generateReport" color="primary">
                        Generate Report
                    </x-filament::button>
                </div>
            </div>
        </x-filament::section>

        <!-- Report Results -->
        @if (!empty($reportData))
            <x-filament::section>
                <x-slot name="heading">
                    {{ $reportData['outlet']->name }} - {{ $reportData['period'] }}
                </x-slot>

                @if (isset($reportData['has_data']) && !$reportData['has_data'])
                    <!-- No Data Message -->
                    <div class="text-center py-12">
                        <div class="mx-auto h-12 w-12 text-gray-400">
                            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            No transaction data available for {{ $reportData['outlet']->name }} in
                            {{ $reportData['period'] }}.
                        </p>
                        <div class="mt-6">
                            <a href="{{ route('filament.akunting.resources.daily-transactions.create') }}"
                                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                Add Transaction
                            </a>
                        </div>
                    </div>
                @else
                    <!-- Detailed P&L Report -->
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                            KETERANGAN
                                        </th>
                                        <th
                                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                            TOTAL
                                        </th>
                                        <th
                                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                            RASIO
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- I. PENDAPATAN -->
                                    <tr class="bg-blue-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">I. PENDAPATAN</td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                    @if ($reportData['revenue_breakdown']['cash'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Cash</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['cash'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['debit'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Debit</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['debit'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['transfer'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Transfer</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['transfer'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['qris'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan QRIS</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['qris'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['gojek'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Gojek</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['gojek'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['grab_ovo'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Grab Ovo</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['grab_ovo'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    @if ($reportData['revenue_breakdown']['sewa_rak'] > 0)
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Sewa Rak</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['revenue_breakdown']['sewa_rak'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    @endif
                                    <tr class="bg-green-50 font-semibold">
                                        <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Pendapatan</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            {{ number_format($reportData['summary']['total_revenue'], 0, ',', '.') }}
                                        </td>
                                        <td class="px-6 py-3"></td>
                                    </tr>

                                    <!-- II. BEBAN PENJUALAN -->
                                    <tr class="bg-red-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">II. BEBAN PENJUALAN</td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                    @if ($reportData['expense_breakdown']['beban_bahan_baku']['total'] > 0)
                                        @foreach ($reportData['expense_breakdown']['beban_bahan_baku']['details'] as $key => $amount)
                                            @if ($amount > 0)
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        @switch($key)
                                                            @case('tagihan_rkv')
                                                                Tagihan RKV
                                                            @break

                                                            @case('tagihan_mitra')
                                                                Tagihan Mitra
                                                            @break

                                                            @case('tagihan_supplier')
                                                                Tagihan Supplier
                                                            @break

                                                            @case('bahan_baku_lainnya')
                                                                Bahan Baku Lainnya
                                                            @break
                                                        @endswitch
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        {{ number_format($amount, 0, ',', '.') }}</td>
                                                    <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                        {{ $this->getPercentageDisplay($amount, $reportData['summary']['total_revenue']) }}
                                                    </td>
                                                </tr>
                                            @endif
                                        @endforeach
                                        <tr class="bg-red-100 font-semibold">
                                            <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Beban Bahan Baku
                                            </td>
                                            <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                                {{ number_format($reportData['expense_breakdown']['beban_bahan_baku']['total'], 0, ',', '.') }}
                                            </td>
                                            <td class="px-6 py-3 text-sm text-gray-600 text-right font-semibold">
                                                {{ $this->getPercentageDisplay($reportData['expense_breakdown']['beban_bahan_baku']['total'], $reportData['summary']['total_revenue']) }}
                                            </td>
                                        </tr>
                                    @endif
                                    <tr class="bg-green-100 font-bold">
                                        <td class="px-6 py-3 text-sm text-gray-900">Laba Kotor</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            {{ number_format($reportData['summary']['laba_kotor'], 0, ',', '.') }}</td>
                                        <td class="px-6 py-3 text-sm text-gray-600 text-right font-bold">
                                            {{ $this->getPercentageDisplay($reportData['summary']['laba_kotor'], $reportData['summary']['total_revenue']) }}
                                        </td>
                                    </tr>

                                    <!-- III. BEBAN OPERASIONAL -->
                                    <tr class="bg-orange-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">III. BEBAN OPERASIONAL
                                        </td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Category Breakdown -->
                    @if (!empty($reportData['by_category']))
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-4">Breakdown by Category</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Category</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Revenue</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Expense</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Receivable</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Cash Deficit</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Net</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach ($reportData['by_category'] as $categoryName => $categoryData)
                                            @php
                                                $net = $categoryData['revenue'] - $categoryData['expense'];
                                            @endphp
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span
                                                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    {{ $categoryName === 'FnB' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                        {{ $categoryName }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                                    Rp {{ number_format($categoryData['revenue'], 0, ',', '.') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                    Rp {{ number_format($categoryData['expense'], 0, ',', '.') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                    Rp {{ number_format($categoryData['receivable'], 0, ',', '.') }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                    Rp {{ number_format($categoryData['cash_deficit'], 0, ',', '.') }}
                                                </td>
                                                <td
                                                    class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $net >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                    Rp {{ number_format($net, 0, ',', '.') }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif

                    <!-- Transaction Details -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Transaction Details</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Category</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Type</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach ($reportData['transactions']->sortBy('transaction_date') as $transaction)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $transaction->transaction_date->format('d/m/Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                {{ $transaction->outlet->category === 'FnB' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                    {{ $transaction->outlet->category }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                {{ $transaction->description }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @switch($transaction->type)
                                                    @case('revenue') bg-green-100 text-green-800 @break
                                                    @case('expense') bg-red-100 text-red-800 @break
                                                    @case('receivable') bg-yellow-100 text-yellow-800 @break
                                                    @case('cash_deficit') bg-gray-100 text-gray-800 @break
                                                @endswitch">
                                                    {{ $transaction->type_display }}
                                                </span>
                                            </td>
                                            <td
                                                class="px-6 py-4 whitespace-nowrap text-sm font-medium
                                            @switch($transaction->type)
                                                @case('revenue') text-green-600 @break
                                                @case('expense') text-red-600 @break
                                                @case('receivable') text-yellow-600 @break
                                                @case('cash_deficit') text-gray-600 @break
                                            @endswitch">
                                                Rp {{ number_format($transaction->amount, 0, ',', '.') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
