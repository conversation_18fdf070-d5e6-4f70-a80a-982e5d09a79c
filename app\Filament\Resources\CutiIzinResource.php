<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CutiIzinResource\Pages;
use App\Models\CutiIzin;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use App\Traits\HasExportActions;
use App\Services\PermissionService;

class CutiIzinResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = CutiIzin::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $navigationLabel = 'Cuti, Izin & Sakit';
    protected static ?int $navigationSort = 4;

    // has access
    public static function canAccess(): bool
    {
        return !(Auth::user()->hasRole(['karyawan']));
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Permohonan')
                    ->schema([
                        Forms\Components\Select::make('karyawan_id')
                            ->label('Karyawan')
                            ->options(function () {
                                $user = Auth::user();
                                $query = Karyawan::query()
                                    ->where('status_aktif', 1)
                                    ->orderBy('nama_lengkap');

                                if ($user->role === 'keptok') {
                                    // Keptok hanya bisa buat cuti/izin untuk karyawan di entitas mereka
                                    if ($user->karyawan && $user->karyawan->id_entitas) {
                                        $query->where('id_entitas', $user->karyawan->id_entitas);
                                        // Exclude diri sendiri
                                        $query->where('id', '!=', $user->karyawan->id);
                                    } else {
                                        // Jika keptok tidak punya karyawan record, tidak bisa pilih siapa-siapa
                                        $query->whereRaw('1 = 0');
                                    }
                                } elseif ($user->role === 'supervisor') {
                                    // Supervisor hanya bisa buat cuti/izin untuk karyawan di divisi mereka
                                    if ($user->karyawan && $user->karyawan->id_divisi) {
                                        $query->where('id_divisi', $user->karyawan->id_divisi);
                                        // Exclude diri sendiri
                                        $query->where('id', '!=', $user->karyawan->id);
                                    } else {
                                        $query->whereRaw('1 = 0');
                                    }
                                } elseif ($user->role === 'manager') {
                                    // Manager hanya bisa buat cuti/izin untuk karyawan di departemen mereka
                                    if ($user->karyawan && $user->karyawan->id_departemen) {
                                        $query->where('id_departemen', $user->karyawan->id_departemen);
                                        // Exclude diri sendiri
                                        $query->where('id', '!=', $user->karyawan->id);
                                    } else {
                                        $query->whereRaw('1 = 0');
                                    }
                                } elseif (in_array($user->role, ['admin']) || $user->hasAnyRole(['manager_hrd', 'super_admin'])) {
                                    // Admin, Manager HRD, Super Admin dapat membuat cuti/izin untuk semua karyawan
                                    // No additional filtering needed
                                }

                                return $query->pluck('nama_lengkap', 'id');
                            })
                            ->required()
                            ->searchable(),

                        Forms\Components\Select::make('jenis_permohonan')
                            ->label('Jenis Permohonan')
                            ->options([
                                'cuti' => 'Cuti',
                                'izin' => 'Izin',
                                'sakit' => 'Sakit',
                            ])
                            ->required(),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('tanggal_mulai')
                                    ->label('Tanggal Mulai')
                                    ->required(),

                                Forms\Components\DatePicker::make('tanggal_selesai')
                                    ->label('Tanggal Selesai')
                                    ->required(),
                            ]),

                        Forms\Components\TextInput::make('jumlah_hari')
                            ->label('Jumlah Hari Kerja')
                            ->numeric()
                            ->disabled()
                            ->helperText('Akan dihitung otomatis'),

                        Forms\Components\Textarea::make('alasan')
                            ->label('Alasan')
                            ->required()
                            ->maxLength(1000)
                            ->rows(3),

                        Forms\Components\Textarea::make('keterangan_tambahan')
                            ->label('Keterangan Tambahan')
                            ->maxLength(1000)
                            ->rows(2),

                        Forms\Components\FileUpload::make('dokumen_pendukung')
                            ->label('Dokumen Pendukung')
                            ->directory('cuti-izin/dokumen')
                            ->disk('public')
                            ->maxSize(5120)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf']),
                    ]),

                Forms\Components\Section::make('Status Persetujuan')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Menunggu Persetujuan',
                                'approved' => 'Disetujui',
                                'rejected' => 'Ditolak',
                            ])
                            ->required()
                            ->default('pending'),

                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->maxLength(1000)
                            ->visible(fn(callable $get) => $get('status') === 'rejected')
                            ->required(fn(callable $get) => $get('status') === 'rejected'),
                    ])
                    ->visible(fn($record) => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('karyawan.nip')
                    ->label('NIP')
                    ->searchable()
                    ->toggleable(),

                // depatement, divisi, dan jabatan
                Tables\Columns\TextColumn::make('karyawan.departemen.nama_departemen')
                    ->label('Departemen')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('karyawan.divisi.nama_divisi')
                    ->label('Divisi')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('jenis_permohonan')
                    ->label('Jenis')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'cuti' => 'primary',
                        'izin' => 'info',
                        'sakit' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('formatted_date_range')
                    ->label('Periode')
                    ->sortable(['tanggal_mulai']),

                Tables\Columns\TextColumn::make('jumlah_hari')
                    ->label('Jumlah Hari')
                    ->suffix(' hari')
                    ->alignCenter()
                    ->sortable(),

                Tables\Columns\TextColumn::make('alasan')
                    ->label('Alasan')
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->alasan;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('approvedBy.name')
                    ->label('Disetujui Oleh')
                    ->placeholder('-')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('approved_at')
                    ->label('Tanggal Persetujuan')
                    ->dateTime('d M Y H:i')
                    ->placeholder('-')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Pengajuan')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_permohonan')
                    ->label('Jenis Permohonan')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Menunggu Persetujuan',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                    ]),

                Tables\Filters\SelectFilter::make('karyawan_id')
                    ->label('Karyawan')
                    ->options(function () {
                        $user = Auth::user();
                        $query = Karyawan::query()->where('status_aktif', 1);

                        if ($user->role === 'keptok') {
                            // Keptok hanya bisa filter karyawan di entitas mereka
                            if ($user->karyawan && $user->karyawan->id_entitas) {
                                $query->where('id_entitas', $user->karyawan->id_entitas);
                            } else {
                                $query->whereRaw('1 = 0');
                            }
                        } elseif ($user->role === 'supervisor') {
                            // Supervisor hanya bisa filter karyawan di divisi mereka
                            if ($user->karyawan && $user->karyawan->id_divisi) {
                                $query->where('id_divisi', $user->karyawan->id_divisi);
                            } else {
                                $query->whereRaw('1 = 0');
                            }
                        } elseif ($user->role === 'manager') {
                            // Manager hanya bisa filter karyawan di departemen mereka
                            if ($user->karyawan && $user->karyawan->id_departemen) {
                                $query->where('id_departemen', $user->karyawan->id_departemen);
                            } else {
                                $query->whereRaw('1 = 0');
                            }
                        } elseif (in_array($user->role, ['admin']) || $user->hasAnyRole(['manager_hrd', 'super_admin'])) {
                            // Admin, Manager HRD, Super Admin dapat filter semua karyawan
                            // No additional filtering needed
                        }

                        return $query->pluck('nama_lengkap', 'id');
                    })
                    ->searchable(),

                Tables\Filters\Filter::make('tanggal_mulai')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_mulai', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_selesai', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(function ($record) {
                        $user = Auth::user();
                        $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
                        $allowedShieldRoles = ['manager_hrd', 'super_admin'];

                        return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
                            && $record->status === 'pending';
                    })
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                            'rejection_reason' => null,
                        ]);

                        Notification::make()
                            ->title('Permohonan berhasil disetujui')
                            ->body('Permohonan ' . $record->jenis_permohonan_label . ' dari ' . $record->karyawan->nama_lengkap . ' telah disetujui.')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(function ($record) {
                        $user = Auth::user();
                        $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
                        $allowedShieldRoles = ['manager_hrd', 'super_admin'];

                        return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
                            && $record->status === 'pending';
                    })
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->maxLength(1000)
                            ->placeholder('Jelaskan alasan penolakan permohonan ini'),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'status' => 'rejected',
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                            'rejection_reason' => $data['rejection_reason'],
                        ]);

                        Notification::make()
                            ->title('Permohonan berhasil ditolak')
                            ->body('Permohonan ' . $record->jenis_permohonan_label . ' dari ' . $record->karyawan->nama_lengkap . ' telah ditolak.')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCutiIzins::route('/'),
            'create' => Pages\CreateCutiIzin::route('/create'),
            'view' => Pages\ViewCutiIzin::route('/{record}'),
            'edit' => Pages\EditCutiIzin::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return PermissionService::applyPermissionFilter(
            parent::getEloquentQuery(),
            'view_absensi',
            'karyawan_id'
        );
    }

    // public static function getEloquentQuery(): Builder
    // {
    //     // return PermissionService::applyPermissionFilter(
    //     //     parent::getEloquentQuery(),
    //     //     'view_absensi',
    //     //     'karyawan_id'
    //     // );

    //     return PermissionService::applyPermissionFilter(
    //         parent::getEloquentQuery(),
    //         'view_absensi',
    //         'karyawan_id'
    //     );
    //     // $query = parent::getEloquentQuery()
    //     //     ->with([
    //     //         'karyawan:id,nama_lengkap,nip,supervisor_id',
    //     //         'approvedBy:id,name'
    //     //     ]);


    //     // return $query;
    // }
}
