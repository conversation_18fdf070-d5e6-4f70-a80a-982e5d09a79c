<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'outlet_id',
        'karyawan_id',
        'transaction_date',
        'description',
        'type',
        'amount',
        'notes',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function outlet()
    {
        return $this->belongsTo(Outlet::class);
    }

    public function karyawan()
    {
        return $this->belongsTo(\App\Models\Karyawan::class);
    }



    // Scopes
    public function scopeRevenue($query)
    {
        return $query->where('type', 'revenue');
    }

    public function scopeExpense($query)
    {
        return $query->where('type', 'expense');
    }

    public function scopeReceivable($query)
    {
        return $query->where('type', 'receivable');
    }

    public function scopeCashDeficit($query)
    {
        return $query->where('type', 'cash_deficit');
    }

    public function scopeForMonth($query, $year, $month)
    {
        return $query->whereYear('transaction_date', $year)
            ->whereMonth('transaction_date', $month);
    }

    public function scopeForOutlet($query, $outletId)
    {
        return $query->where('outlet_id', $outletId);
    }

    public function scopeForCategory($query, $category)
    {
        return $query->whereHas('outlet', function ($q) use ($category) {
            $q->where('category', $category);
        });
    }

    // Helper methods
    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function getTypeDisplayAttribute()
    {
        return match ($this->type) {
            'revenue' => 'Pendapatan',
            'expense' => 'Pengeluaran',
            'receivable' => 'Piutang',
            'cash_deficit' => 'Kekurangan Kas',
            default => $this->type
        };
    }
}
