<?php

namespace App\Filament\Akunting\Resources\DailyTransactionResource\Pages;

use App\Filament\Akunting\Resources\DailyTransactionResource;
use App\Models\DailyTransaction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ListDailyTransactions extends ListRecords
{
    protected static string $resource = DailyTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Semua')
                ->badge(DailyTransaction::count())
                ->badgeColor('primary'),
            'revenue' => Tab::make('Pendapatan')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'revenue'))
                ->badge(DailyTransaction::where('type', 'revenue')->count())
                ->badgeColor('success'),
            'expense' => Tab::make('Pengeluaran')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'expense'))
                ->badge(DailyTransaction::where('type', 'expense')->count())
                ->badgeColor('danger'),
            'receivable' => Tab::make('Piutang')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'receivable'))
                ->badge(DailyTransaction::where('type', 'receivable')->count())
                ->badgeColor('warning'),
            'cash_deficit' => Tab::make('Kekurangan Kas')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'cash_deficit'))
                ->badge(DailyTransaction::where('type', 'cash_deficit')->count())
                ->badgeColor('gray'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DailyTransactionResource\Widgets\TransactionStatsWidget::class,
        ];
    }
}
