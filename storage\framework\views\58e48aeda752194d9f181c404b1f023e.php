<div x-data="karyawanHierarchySelector()" class="space-y-4">
    <!--[if BLOCK]><![endif]--><?php if($hierarchyGroups->isEmpty()): ?>
        <div class="text-center py-8 text-gray-500">
            <div class="text-4xl mb-2">👥</div>
            <!--[if BLOCK]><![endif]--><?php if(empty($entitasId)): ?>
                <p>Pilih entitas terlebih dahulu</p>
                <p class="text-sm">Pilih entitas untuk menampilkan karyawan yang dapat dipilih</p>
            <?php else: ?>
                <p>Tidak ada karyawan yang dapat dipilih</p>
                <p class="text-sm">Pastikan Anda memiliki permission untuk mengelola jadwal</p>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php else: ?>
        <!-- Summary Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <span class="text-blue-600">📊</span>
                    <span class="font-medium text-blue-800">
                        Total: <?php echo e($hierarchyGroups->flatten(3)->count()); ?> karyawan dalam
                        <?php echo e($hierarchyGroups->count()); ?> entitas
                    </span>
                </div>
                <div class="text-sm text-blue-600">
                    <span x-text="selectedCount"></span> karyawan dipilih
                </div>
            </div>
        </div>

        <!-- Hierarchy Groups: Entitas > Departemen > Divisi -->
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $hierarchyGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entitasName => $departemenGroups): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="border-2 border-indigo-200 rounded-xl overflow-hidden mb-6">
                <!-- Entitas Header -->
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🏢</span>
                            <div>
                                <h2 class="text-xl font-bold"><?php echo e($entitasName); ?></h2>
                                <p class="text-indigo-100"><?php echo e($departemenGroups->flatten(2)->count()); ?> karyawan total
                                </p>
                            </div>
                        </div>

                        <!-- Select All for this Entitas -->
                        <div class="flex items-center space-x-2">
                            <button type="button" @click="selectAllInGroup('entitas', '<?php echo e($entitasName); ?>')"
                                class="text-xs bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-full transition-colors">
                                ✓ Pilih Semua Entitas
                            </button>
                            <button type="button" @click="deselectAllInGroup('entitas', '<?php echo e($entitasName); ?>')"
                                class="text-xs bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-full transition-colors">
                                ✗ Hapus Semua
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Departemen Groups -->
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $departemenGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $departemenName => $divisiGroups): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="border-l-4 border-blue-300 ml-4 mr-4 mt-4 rounded-lg overflow-hidden">
                        <!-- Departemen Header -->
                        <div class="bg-blue-50 border-b border-blue-200 p-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <span class="text-lg">🏛️</span>
                                    <div>
                                        <h3 class="font-semibold text-blue-900"><?php echo e($departemenName); ?></h3>
                                        <p class="text-sm text-blue-600"><?php echo e($divisiGroups->flatten()->count()); ?>

                                            karyawan</p>
                                    </div>
                                </div>

                                <!-- Select All for this Departemen -->
                                <div class="flex items-center space-x-2">
                                    <button type="button"
                                        @click="selectAllInGroup('departemen', '<?php echo e($departemenName); ?>')"
                                        class="text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-full transition-colors">
                                        ✓ Pilih Departemen
                                    </button>
                                    <button type="button"
                                        @click="deselectAllInGroup('departemen', '<?php echo e($departemenName); ?>')"
                                        class="text-xs bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-full transition-colors">
                                        ✗ Hapus Semua
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Divisi Groups -->
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $divisiGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $divisiName => $karyawanList): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border-l-4 border-green-300 ml-4 mr-4 mt-3 mb-3 rounded-lg overflow-hidden">
                                <!-- Divisi Header -->
                                <div class="bg-green-50 border-b border-green-200 p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="text-lg">📂</span>
                                            <div>
                                                <h4 class="font-semibold text-green-900"><?php echo e($divisiName); ?></h4>
                                                <p class="text-sm text-green-600"><?php echo e($karyawanList->count()); ?> karyawan
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Select All for this Divisi -->
                                        <div class="flex items-center space-x-2">
                                            <button type="button"
                                                @click="selectAllInGroup('divisi', '<?php echo e($divisiName); ?>')"
                                                class="text-xs bg-green-100 hover:bg-green-200 text-green-700 px-3 py-1 rounded-full transition-colors">
                                                ✓ Pilih Divisi
                                            </button>
                                            <button type="button"
                                                @click="deselectAllInGroup('divisi', '<?php echo e($divisiName); ?>')"
                                                class="text-xs bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-full transition-colors">
                                                ✗ Hapus Semua
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Karyawan List -->
                                <div class="p-3 bg-gray-50">
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $karyawanList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $karyawan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <label
                                                class="flex items-center space-x-3 p-2 rounded-lg border border-gray-200 bg-white hover:bg-gray-50 cursor-pointer transition-colors">
                                                <input type="checkbox" value="<?php echo e($karyawan->id); ?>"
                                                    x-model="selectedKaryawan" @change="updateHiddenField()"
                                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                    data-entitas="<?php echo e($entitasName); ?>"
                                                    data-departemen="<?php echo e($departemenName); ?>"
                                                    data-divisi="<?php echo e($divisiName); ?>">
                                                <div class="flex-1 min-w-0">
                                                    <div class="font-medium text-gray-900 truncate text-sm">
                                                        <?php echo e($karyawan->nama_lengkap); ?>

                                                    </div>
                                                    <div class="text-xs text-gray-500">
                                                        <?php echo e($karyawan->nip); ?>

                                                    </div>
                                                </div>
                                            </label>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<script>
    function karyawanHierarchySelector() {
        return {
            selectedKaryawan: <?php echo \Illuminate\Support\Js::from($selectedKaryawan ?? [])->toHtml() ?>,

            get selectedCount() {
                return this.selectedKaryawan.length;
            },

            selectAllInGroup(groupType, groupName) {
                let selector = '';
                switch (groupType) {
                    case 'entitas':
                        selector = `input[data-entitas="${groupName}"]`;
                        break;
                    case 'departemen':
                        selector = `input[data-departemen="${groupName}"]`;
                        break;
                    case 'divisi':
                        selector = `input[data-divisi="${groupName}"]`;
                        break;
                }

                const checkboxes = document.querySelectorAll(selector);
                checkboxes.forEach(checkbox => {
                    const value = parseInt(checkbox.value);
                    if (!this.selectedKaryawan.includes(value)) {
                        this.selectedKaryawan.push(value);
                        checkbox.checked = true;
                    }
                });
                this.updateHiddenField();
            },

            deselectAllInGroup(groupType, groupName) {
                let selector = '';
                switch (groupType) {
                    case 'entitas':
                        selector = `input[data-entitas="${groupName}"]`;
                        break;
                    case 'departemen':
                        selector = `input[data-departemen="${groupName}"]`;
                        break;
                    case 'divisi':
                        selector = `input[data-divisi="${groupName}"]`;
                        break;
                }

                const checkboxes = document.querySelectorAll(selector);
                checkboxes.forEach(checkbox => {
                    const value = parseInt(checkbox.value);
                    const index = this.selectedKaryawan.indexOf(value);
                    if (index > -1) {
                        this.selectedKaryawan.splice(index, 1);
                        checkbox.checked = false;
                    }
                });
                this.updateHiddenField();
            },

            updateHiddenField() {
                // Try multiple selectors to find the karyawan field
                const selectors = [
                    'input[name="karyawan"]',
                    'input[wire\\:model="data.karyawan"]',
                    'input[wire\\:model\\.defer="data.karyawan"]',
                    'input[id*="karyawan"]',
                    'input[data-field="karyawan"]'
                ];

                let hiddenField = null;
                for (const selector of selectors) {
                    hiddenField = document.querySelector(selector);
                    if (hiddenField) break;
                }

                if (hiddenField) {
                    const jsonValue = JSON.stringify(this.selectedKaryawan);
                    hiddenField.value = jsonValue;

                    // Trigger multiple events to ensure Filament/Livewire picks it up
                    const events = ['input', 'change', 'blur', 'keyup'];
                    events.forEach(eventType => {
                        hiddenField.dispatchEvent(new Event(eventType, {
                            bubbles: true
                        }));
                    });

                    // Force Livewire update if available
                    if (window.Livewire && hiddenField.hasAttribute('wire:model')) {
                        const component = hiddenField.closest('[wire\\:id]');
                        if (component) {
                            const componentId = component.getAttribute('wire:id');
                            window.Livewire.find(componentId).set('data.karyawan', jsonValue);
                        }
                    }
                }
            },

            init() {
                // Initialize checkboxes based on selectedKaryawan
                this.$nextTick(() => {
                    this.selectedKaryawan.forEach(id => {
                        const checkbox = document.querySelector(`input[value="${id}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                });
            }
        }
    }
</script>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/forms/karyawan-divisi-selector.blade.php ENDPATH**/ ?>