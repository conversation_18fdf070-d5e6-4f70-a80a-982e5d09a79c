<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Outlet;

class OutletSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $outlets = [
            [
                'name' => 'Outlet Jakarta Pusat',
                'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                'description' => 'Outlet utama di Jakarta Pusat dengan fokus FnB dan layanan lengkap',
                'is_active' => true,
            ],
            [
                'name' => 'Outlet Bandung',
                'address' => 'Jl. Asia Afrika No. 45, Bandung',
                'description' => 'Outlet cabang di Bandung dengan layanan FnB dan VOO',
                'is_active' => true,
            ],
            [
                'name' => 'Outlet Surabaya',
                'address' => 'Jl. Pemuda No. 67, Surabaya',
                'description' => 'Outlet cabang di Surabaya dengan fokus pada layanan VOO',
                'is_active' => true,
            ],
            [
                'name' => 'Outlet Medan',
                'address' => 'Jl. Gatot Subroto No. 89, Medan',
                'description' => 'Outlet cabang di Medan dengan layanan terbatas',
                'is_active' => false,
            ],
        ];

        foreach ($outlets as $outlet) {
            Outlet::create($outlet);
        }
    }
}
