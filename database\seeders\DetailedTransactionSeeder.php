<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use Carbon\Carbon;

class DetailedTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $outlets = Outlet::all();
        $currentMonth = now();

        foreach ($outlets as $outlet) {
            // Sample revenue transactions with payment methods
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan Cash Harian',
                'type' => 'revenue',
                'payment_method' => 'cash',
                'amount' => 454095300,
                'notes' => 'Penjualan tunai'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan Debit',
                'type' => 'revenue',
                'payment_method' => 'debit',
                'amount' => ********,
                'notes' => 'Penjualan kartu debit'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan Transfer',
                'type' => 'revenue',
                'payment_method' => 'transfer',
                'amount' => ********,
                'notes' => 'Penjualan transfer bank'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(5),
                'description' => 'Penjualan QRIS',
                'type' => 'revenue',
                'payment_method' => 'qris',
                'amount' => *********,
                'notes' => 'Penjualan QRIS'
            ]);

            // Sample expense transactions with categories and subcategories
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Tagihan RKV',
                'type' => 'expense',
                'expense_category' => 'beban_bahan_baku',
                'subcategory' => 'tagihan_rkv',
                'amount' => *********,
                'notes' => 'Pembayaran tagihan RKV'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(3),
                'description' => 'Tagihan Mitra Juni 2025',
                'type' => 'expense',
                'expense_category' => 'beban_bahan_baku',
                'subcategory' => 'tagihan_mitra',
                'amount' => 224142500,
                'notes' => 'Pembayaran tagihan mitra'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(2),
                'description' => 'Bayar Listrik',
                'type' => 'expense',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'listrik',
                'amount' => 7224654,
                'notes' => 'Pembayaran listrik bulanan'
            ]);

            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $currentMonth->copy()->subDays(1),
                'description' => 'Gaji Karyawan Juni 2025',
                'type' => 'expense',
                'expense_category' => 'gaji',
                'subcategory' => 'gaji_tetap',
                'amount' => 38567576,
                'notes' => 'Pembayaran gaji karyawan'
            ]);
        }

        $this->command->info('Sample detailed transactions created successfully!');
    }
}
