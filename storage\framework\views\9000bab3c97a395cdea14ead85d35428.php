<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Filter Form -->
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> 
                Filter Report
             <?php $__env->endSlot(); ?>

            <form wire:submit.prevent="generateReport">
                <?php echo e($this->form); ?>


                <div class="mt-4">
                    <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['type' => 'submit','color' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','color' => 'primary']); ?>
                        Generate Report
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                </div>
            </form>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

        <!-- Report Results -->
        <!--[if BLOCK]><![endif]--><?php if(!empty($reportData)): ?>
            <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                 <?php $__env->slot('heading', null, []); ?> 
                    <?php echo e($reportData['outlet']->name); ?> - <?php echo e($reportData['period']); ?>

                 <?php $__env->endSlot(); ?>

                <!-- Detailed P&L Report -->
                <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                        KETERANGAN
                                    </th>
                                    <th
                                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                        TOTAL
                                    </th>
                                    <th
                                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                        RASIO
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- I. PENDAPATAN -->
                                <tr class="bg-blue-50">
                                    <td class="px-6 py-3 text-sm font-bold text-gray-900">I. PENDAPATAN</td>
                                    <td class="px-6 py-3"></td>
                                    <td class="px-6 py-3"></td>
                                </tr>
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['cash'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Cash</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['cash'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['debit'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Debit</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['debit'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['transfer'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Transfer</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['transfer'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['qris'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan QRIS</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['qris'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['gojek'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Gojek</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['gojek'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['grab_ovo'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Grab Ovo</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['grab_ovo'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['sewa_rak'] > 0): ?>
                                    <tr>
                                        <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Sewa Rak</td>
                                        <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['revenue_breakdown']['sewa_rak'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-2"></td>
                                    </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <tr class="bg-green-50 font-semibold">
                                    <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Pendapatan</td>
                                    <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                        <?php echo e(number_format($reportData['summary']['total_revenue'], 0, ',', '.')); ?></td>
                                    <td class="px-6 py-3"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Category Breakdown -->
                <!--[if BLOCK]><![endif]--><?php if(!empty($reportData['by_category'])): ?>
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">Breakdown by Category</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Category</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Revenue</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Expense</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Receivable</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Cash Deficit</th>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Net</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['by_category']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryName => $categoryData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $net = $categoryData['revenue'] - $categoryData['expense'];
                                        ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    <?php echo e($categoryName === 'FnB' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                                    <?php echo e($categoryName); ?>

                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                                Rp <?php echo e(number_format($categoryData['revenue'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                Rp <?php echo e(number_format($categoryData['expense'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                Rp <?php echo e(number_format($categoryData['receivable'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                Rp <?php echo e(number_format($categoryData['cash_deficit'], 0, ',', '.')); ?>

                                            </td>
                                            <td
                                                class="px-6 py-4 whitespace-nowrap text-sm font-medium <?php echo e($net >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                                Rp <?php echo e(number_format($net, 0, ',', '.')); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Transaction Details -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Transaction Details</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Category</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Description</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['transactions']->sortBy('transaction_date'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo e($transaction->transaction_date->format('d/m/Y')); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                <?php echo e($transaction->outlet->category === 'FnB' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                                <?php echo e($transaction->outlet->category); ?>

                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo e($transaction->description); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                <?php switch($transaction->type):
                                                    case ('revenue'): ?> bg-green-100 text-green-800 <?php break; ?>
                                                    <?php case ('expense'): ?> bg-red-100 text-red-800 <?php break; ?>
                                                    <?php case ('receivable'): ?> bg-yellow-100 text-yellow-800 <?php break; ?>
                                                    <?php case ('cash_deficit'): ?> bg-gray-100 text-gray-800 <?php break; ?>
                                                <?php endswitch; ?>">
                                                <?php echo e($transaction->type_display); ?>

                                            </span>
                                        </td>
                                        <td
                                            class="px-6 py-4 whitespace-nowrap text-sm font-medium
                                            <?php switch($transaction->type):
                                                case ('revenue'): ?> text-green-600 <?php break; ?>
                                                <?php case ('expense'): ?> text-red-600 <?php break; ?>
                                                <?php case ('receivable'): ?> text-yellow-600 <?php break; ?>
                                                <?php case ('cash_deficit'): ?> text-gray-600 <?php break; ?>
                                            <?php endswitch; ?>">
                                            Rp <?php echo e(number_format($transaction->amount, 0, ',', '.')); ?>

                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/akunting/pages/monthly-outlet-report.blade.php ENDPATH**/ ?>