<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTransaction;
use App\Models\Outlet;
use Carbon\Carbon;

class DetailedMonthlyTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $outlets = Outlet::all();
        $currentMonth = now();

        foreach ($outlets as $outlet) {
            // Sample detailed expense transactions per outlet per month
            $this->createDetailedExpenses($outlet, $currentMonth);
        }

        $this->command->info('Detailed monthly transactions created successfully!');
    }

    private function createDetailedExpenses(Outlet $outlet, Carbon $month)
    {
        $transactions = [
            // Beban Utilitas
            [
                'description' => 'Bayar Listrik Bulanan',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'listrik',
                'amount' => 7224654,
            ],
            [
                'description' => 'Bayar Indihome dan Pulsa',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'internet_pulsa',
                'amount' => 324700,
            ],
            [
                'description' => 'Jasa Pengendalian Hama (Petsco)',
                'expense_category' => 'beban_utilitas',
                'subcategory' => 'pest_control',
                'amount' => 700000,
            ],

            // Beban GA
            [
                'description' => 'Belanja GA Bulanan',
                'expense_category' => 'beban_ga',
                'subcategory' => 'belanja_ga',
                'amount' => 500000,
            ],
            [
                'description' => 'Belanja Aset GA',
                'expense_category' => 'beban_ga',
                'subcategory' => 'belanja_aset_ga',
                'amount' => 2500000,
            ],
            [
                'description' => 'Harau Sticker dan Banner',
                'expense_category' => 'beban_ga',
                'subcategory' => 'harau_sticker_banner',
                'amount' => 850000,
            ],
            [
                'description' => 'Material Bangunan dan Kasbon Tukang',
                'expense_category' => 'beban_ga',
                'subcategory' => 'material_bangunan',
                'amount' => 1330000,
            ],
            [
                'description' => 'Service Freezer dan AC',
                'expense_category' => 'beban_ga',
                'subcategory' => 'service_equipment',
                'amount' => 1200000,
            ],
            [
                'description' => 'Kertas Thermal dan Nota',
                'expense_category' => 'beban_ga',
                'subcategory' => 'kertas_thermal',
                'amount' => 525000,
            ],
            [
                'description' => 'Service Kompor dan Freezer',
                'expense_category' => 'beban_ga',
                'subcategory' => 'service_kompor_freezer',
                'amount' => 800000,
            ],
            [
                'description' => 'Bensin Luxio Baru',
                'expense_category' => 'beban_ga',
                'subcategory' => 'bensin_luxio',
                'amount' => 2867000,
            ],
            [
                'description' => 'Konten Video dan Foto Produk',
                'expense_category' => 'beban_ga',
                'subcategory' => 'konten_video_foto',
                'amount' => 1500000,
            ],
            [
                'description' => 'Biaya Pengurusan LPPOM MUI RIAU',
                'expense_category' => 'beban_ga',
                'subcategory' => 'lppom_mui',
                'amount' => 3000000,
            ],
            [
                'description' => 'Konsumsi Meeting Direktur',
                'expense_category' => 'beban_ga',
                'subcategory' => 'konsumsi_meeting',
                'amount' => 750000,
            ],

            // Pajak
            [
                'description' => 'Bayar Pajak Bapenda dan Fee',
                'expense_category' => 'pajak',
                'subcategory' => 'pajak_bapenda',
                'amount' => 7468491,
            ],

            // BPJS
            [
                'description' => 'Bayar BPJS TK',
                'expense_category' => 'bpjs',
                'subcategory' => 'bpjs_tk',
                'amount' => 149108,
            ],

            // Komisi Bank
            [
                'description' => 'Komisi Bank dan Gojek',
                'expense_category' => 'komisi_bank',
                'subcategory' => 'komisi_bank_gojek',
                'amount' => ********,
            ],

            // Gaji
            [
                'description' => 'Gaji Karyawan Bulanan',
                'expense_category' => 'gaji',
                'subcategory' => 'gaji_karyawan',
                'amount' => ********,
            ],
            [
                'description' => 'Gaji Part Time dan Freelance',
                'expense_category' => 'gaji',
                'subcategory' => 'gaji_part_time',
                'amount' => 5500000,
            ],
        ];

        foreach ($transactions as $index => $transactionData) {
            DailyTransaction::create([
                'outlet_id' => $outlet->id,
                'transaction_date' => $month->copy()->subDays(rand(1, 28)),
                'description' => $transactionData['description'],
                'type' => 'expense',
                'expense_category' => $transactionData['expense_category'],
                'subcategory' => $transactionData['subcategory'],
                'amount' => $transactionData['amount'],
                'notes' => 'Sample data untuk laporan P&L bulanan',
            ]);
        }
    }
}
