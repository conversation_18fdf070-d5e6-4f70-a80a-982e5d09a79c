<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionSubcategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'code',
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(TransactionCategory::class);
    }

    public function transactions()
    {
        return $this->hasMany(DailyTransaction::class, 'subcategory', 'code');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForCategory($query, $categoryCode)
    {
        return $query->whereHas('category', function ($q) use ($categoryCode) {
            $q->where('code', $categoryCode);
        });
    }
}
